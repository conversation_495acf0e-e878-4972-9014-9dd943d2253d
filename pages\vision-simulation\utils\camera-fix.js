/**
 * 相机问题修复工具
 * 专门处理相机启动失败、权限问题等常见问题
 */

const permissionManager = require('./permission-manager');

const cameraFix = {

  /**
   * 诊断相机问题
   * @param {Object} page 页面实例
   * @returns {Promise} 诊断结果
   */
  diagnoseCameraIssue: function(page) {
    return new Promise((resolve) => {
      console.log('开始诊断相机问题...');
      
      const diagnosis = {
        hasPermission: false,
        cameraAvailable: false,
        webglSupported: false,
        issues: [],
        solutions: []
      };
      
      // 1. 检查相机权限
      wx.getSetting({
        success: (res) => {
          console.log('当前权限设置:', res.authSetting);
          
          if (res.authSetting['scope.camera'] === true) {
            diagnosis.hasPermission = true;
            console.log('✓ 相机权限已授权');
          } else if (res.authSetting['scope.camera'] === false) {
            diagnosis.issues.push('相机权限被拒绝');
            diagnosis.solutions.push('需要手动开启相机权限');
            console.log('✗ 相机权限被拒绝');
          } else {
            diagnosis.issues.push('相机权限未申请');
            diagnosis.solutions.push('需要申请相机权限');
            console.log('? 相机权限未申请');
          }
          
          // 2. 检查设备信息
          wx.getSystemInfo({
            success: (sysInfo) => {
              console.log('设备信息:', sysInfo);
              
              // 检查是否支持相机
              if (sysInfo.cameraAuthorized === false) {
                diagnosis.issues.push('设备相机权限被系统拒绝');
                diagnosis.solutions.push('检查系统设置中的相机权限');
              }
              
              // 检查WebGL支持
              try {
                const canvas = wx.createOffscreenCanvas({ type: 'webgl' });
                if (canvas) {
                  diagnosis.webglSupported = true;
                  console.log('✓ WebGL支持正常');
                } else {
                  diagnosis.issues.push('设备不支持WebGL');
                  diagnosis.solutions.push('设备硬件不兼容，建议更换设备');
                }
              } catch (error) {
                diagnosis.issues.push('WebGL检测失败');
                diagnosis.solutions.push('设备可能不支持WebGL');
                console.log('WebGL检测失败:', error);
              }
              
              resolve(diagnosis);
            },
            fail: (error) => {
              console.error('获取设备信息失败:', error);
              diagnosis.issues.push('无法获取设备信息');
              diagnosis.solutions.push('设备可能存在兼容性问题');
              resolve(diagnosis);
            }
          });
        },
        fail: (error) => {
          console.error('获取权限设置失败:', error);
          diagnosis.issues.push('无法获取权限设置');
          diagnosis.solutions.push('小程序可能存在问题，建议重启');
          resolve(diagnosis);
        }
      });
    });
  },

  /**
   * 自动修复相机问题
   * @param {Object} page 页面实例
   * @returns {Promise} 修复结果
   */
  autoFixCamera: function(page) {
    return new Promise(async (resolve, reject) => {
      try {
        console.log('开始自动修复相机问题...');
        
        // 1. 诊断问题
        const diagnosis = await this.diagnoseCameraIssue(page);
        console.log('诊断结果:', diagnosis);
        
        // 2. 根据诊断结果进行修复
        if (!diagnosis.hasPermission) {
          console.log('尝试申请相机权限...');
          
          try {
            const result = await permissionManager.requestCameraPermission();
            console.log('权限申请结果:', result);
            
            // 权限申请成功，重新初始化相机
            setTimeout(() => {
              page.initCamera();
            }, 500);
            
            resolve({
              success: true,
              message: '相机权限申请成功，正在重新初始化相机...'
            });
            
          } catch (permError) {
            console.error('权限申请失败:', permError);
            
            if (permError.errCode === 'auth_rejected' || permError.errCode === 'auth_denied') {
              // 权限被拒绝，需要手动设置
              resolve({
                success: false,
                needManualSetting: true,
                message: '相机权限被拒绝，需要手动开启权限',
                action: 'openSetting'
              });
            } else {
              resolve({
                success: false,
                message: '权限申请失败: ' + permError.errMsg
              });
            }
          }
        } else {
          // 有权限但相机仍然失败，可能是其他问题
          console.log('权限正常，检查其他问题...');
          
          if (!diagnosis.webglSupported) {
            resolve({
              success: false,
              message: '设备不支持WebGL，无法使用视觉功能'
            });
            return;
          }
          
          // 尝试重置相机状态
          this.resetCameraState(page);
          
          setTimeout(() => {
            page.initCamera();
          }, 1000);
          
          resolve({
            success: true,
            message: '正在重新初始化相机...'
          });
        }
        
      } catch (error) {
        console.error('自动修复失败:', error);
        reject({
          success: false,
          message: '自动修复失败: ' + error.message
        });
      }
    });
  },

  /**
   * 手动引导用户开启权限
   * @param {Object} page 页面实例
   * @returns {Promise} 引导结果
   */
  guideUserToEnablePermission: function(page) {
    return new Promise((resolve, reject) => {
      wx.showModal({
        title: '相机权限被拒绝',
        content: '爱宠视觉需要使用相机来模拟宠物视角，请点击"前往设置"开启相机权限',
        confirmText: '前往设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 用户确认前往设置
            permissionManager.openPermissionSetting('camera')
              .then((granted) => {
                if (granted) {
                  // 用户已开启权限
                  wx.showToast({
                    title: '权限开启成功',
                    icon: 'success',
                    duration: 1500
                  });
                  
                  // 重新初始化相机
                  page.setData({
                    cameraError: false,
                    cameraLoading: true,
                    showCameraSettingBtn: false
                  });
                  
                  setTimeout(() => {
                    page.initCamera();
                  }, 500);
                  
                  resolve({
                    success: true,
                    message: '权限开启成功，正在重新初始化相机...'
                  });
                } else {
                  // 用户未开启权限
                  wx.showToast({
                    title: '相机权限未开启',
                    icon: 'none',
                    duration: 2000
                  });
                  
                  resolve({
                    success: false,
                    message: '相机权限未开启'
                  });
                }
              })
              .catch((err) => {
                console.error('打开设置页面失败:', err);
                wx.showToast({
                  title: '打开设置页面失败',
                  icon: 'none',
                  duration: 2000
                });
                
                reject({
                  success: false,
                  message: '打开设置页面失败'
                });
              });
          } else {
            // 用户取消
            resolve({
              success: false,
              message: '用户取消开启权限'
            });
          }
        }
      });
    });
  },

  /**
   * 重置相机状态
   * @param {Object} page 页面实例
   */
  resetCameraState: function(page) {
    console.log('重置相机状态...');

    // 停止现有相机
    if (page.cameraCtx) {
      try {
        const cameraManager = require('./camera-manager');
        cameraManager.stopCamera(page.cameraCtx);
      } catch (error) {
        console.error('停止相机时出错:', error);
      }
      page.cameraCtx = null;
    }

    // 清理WebGL上下文
    if (global.webglContext) {
      try {
        const webglRenderer = require('./webgl-renderer');
        webglRenderer.cleanup(global.webglContext);
        global.webglContext = null;
      } catch (error) {
        console.error('清理WebGL资源时出错:', error);
      }
    }

    // 重置页面状态
    page.setData({
      cameraLoading: false,
      cameraError: false,
      cameraErrorMsg: '',
      showCameraSettingBtn: false
    });
  },

  /**
   * 显示修复建议
   * @param {Object} diagnosis 诊断结果
   */
  showFixSuggestions: function(diagnosis) {
    let content = '检测到以下问题：\n';
    diagnosis.issues.forEach((issue, index) => {
      content += `${index + 1}. ${issue}\n`;
    });
    
    content += '\n建议解决方案：\n';
    diagnosis.solutions.forEach((solution, index) => {
      content += `${index + 1}. ${solution}\n`;
    });
    
    wx.showModal({
      title: '相机问题诊断',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    });
  }
};

module.exports = cameraFix;
