/**
 * 视觉参数管理器
 * 负责视觉参数的调整、重置和品种特定配置
 */

const visionConfig = require('./vision-config');

const visionParamsManager = {

  /**
   * 根据动物种类和品种设置视力清晰度因子
   */
  setBreedVisualAcuity: function(page, breedName, animalType) {
    // 默认值
    let resolutionFactor = 0.5;
    
    // 根据动物类型和品种设置不同的清晰度因子
    if (animalType === 'cat') {
      // 猫科动物视力较差，默认设置为1/5 (20/100)
      resolutionFactor = visionConfig.VISUAL_ACUITY_CONFIG.CAT.RESOLUTION_FACTOR;
      
      // 暹罗猫视力较好，设置为15/100
      if (breedName && breedName.includes('暹罗')) {
        resolutionFactor = visionConfig.VISUAL_ACUITY_CONFIG.CAT.SIAMESE_RESOLUTION_FACTOR;
        console.log('设置暹罗猫的视力清晰度因子为:', resolutionFactor);
        
        // 调用暹罗猫的特殊夜视能力设置函数
        this.setSiameseNightVision(page);
      } else {
        console.log('设置猫科动物[' + breedName + ']的视力清晰度因子为:', resolutionFactor);
      }
    } else {
      // 犬科动物视力，默认设置为1/2 (50/100)
      resolutionFactor = visionConfig.VISUAL_ACUITY_CONFIG.DOG.RESOLUTION_FACTOR;
      
      // 哈士奇视力较好，设置为60/100
      if (breedName && breedName.includes('哈士奇')) {
        resolutionFactor = visionConfig.VISUAL_ACUITY_CONFIG.DOG.HUSKY_RESOLUTION_FACTOR;
        console.log('设置哈士奇的视力清晰度因子为:', resolutionFactor);
        
        // 调用哈士奇的特殊夜视能力设置函数
        this.setHuskyNightVision(page);
      } else {
        console.log('设置犬科动物[' + breedName + ']的视力清晰度因子为:', resolutionFactor);
      }
    }
    
    // 更新页面数据
    page.setData({
      'dogVisionParams.resolutionFactor': resolutionFactor
    });
  },

  /**
   * 设置暹罗猫的夜视能力
   */
  setSiameseNightVision: function(page) {
    // 暹罗猫因为眼睛有轻微的视轴对位问题，夜视能力较弱
    const brightness = visionConfig.NIGHT_VISION_CONFIG.CAT.SIAMESE_BRIGHTNESS; // 暹罗猫特殊亮度增强因子：1.5
    const contrast = visionConfig.NIGHT_VISION_CONFIG.CAT.SIAMESE_CONTRAST; // 暹罗猫特殊对比度因子：1.2
    
    console.log('设置暹罗猫的夜视参数 - 亮度:', brightness, '对比度:', contrast);
    
    // 更新页面数据
    page.setData({
      'dogVisionParams.brightness': brightness,
      'dogVisionParams.contrast': contrast
    });
  },

  /**
   * 设置哈士奇的夜视能力
   */
  setHuskyNightVision: function(page) {
    // 哈士奇的夜视能力要比其它品种的犬科动物要强
    // 设置与猫科动物相同的夜视参数
    const brightness = visionConfig.NIGHT_VISION_CONFIG.DOG.HUSKY_BRIGHTNESS; // 哈士奇特殊亮度增强因子：1.8
    const contrast = visionConfig.NIGHT_VISION_CONFIG.DOG.HUSKY_CONTRAST; // 哈士奇特殊对比度因子：1.4
    
    console.log('设置哈士奇的夜视参数 - 亮度:', brightness, '对比度:', contrast);
    
    // 更新页面数据
    page.setData({
      'dogVisionParams.brightness': brightness,
      'dogVisionParams.contrast': contrast
    });
  },

  /**
   * 分辨率参数变化处理
   */
  onResolutionChange: function(page, value) {
    const resolutionFactor = value;
    
    console.log('分辨率参数变化:', resolutionFactor);
    
    // 更新页面数据
    page.setData({
      'dogVisionParams.resolutionFactor': resolutionFactor
    });
    
    // 同步到视觉管理器
    const visionManager = require('./vision-manager');
    if (global.visionContext) {
      visionManager.updateVisionParams(global.visionContext, {
        resolutionFactor: resolutionFactor
      });
    }
  },

  /**
   * 视野因子参数变化处理
   */
  onAntiAliasChange: function(page, value) {
    const viewFieldFactor = value;
    
    console.log('视野因子参数变化:', viewFieldFactor);
    
    // 更新页面数据
    page.setData({
      'dogVisionParams.antiAliasFactor': viewFieldFactor
    });
    
    // 同步到视觉管理器
    const visionManager = require('./vision-manager');
    if (global.visionContext) {
      visionManager.updateVisionParams(global.visionContext, {
        antiAliasFactor: viewFieldFactor
      });
    }
  },

  /**
   * 亮度参数变化处理
   */
  onBrightnessChange: function(page, value) {
    const brightness = 1 + value / 50;
    
    console.log('亮度参数变化:', brightness);
    
    // 更新页面数据
    page.setData({
      'dogVisionParams.brightness': brightness
    });
    
    // 同步到视觉管理器
    const visionManager = require('./vision-manager');
    if (global.visionContext) {
      visionManager.updateVisionParams(global.visionContext, {
        brightness: brightness
      });
    }
  },

  /**
   * 对比度参数变化处理
   */
  onContrastChange: function(page, value) {
    const contrast = 1 + value / 50;
    
    console.log('对比度参数变化:', contrast);
    
    // 更新页面数据
    page.setData({
      'dogVisionParams.contrast': contrast
    });
    
    // 同步到视觉管理器
    const visionManager = require('./vision-manager');
    if (global.visionContext) {
      visionManager.updateVisionParams(global.visionContext, {
        contrast: contrast
      });
    }
  },

  /**
   * 运动敏感度参数变化处理
   */
  onMotionSensitivityChange: function(page, value) {
    console.log('运动敏感度参数变化:', value);
    
    // 更新页面数据
    page.setData({
      'dogVisionParams.motionSensitivity': value
    });
    
    // 同步到视觉管理器
    const visionManager = require('./vision-manager');
    if (global.visionContext) {
      visionManager.updateVisionParams(global.visionContext, {
        motionSensitivity: value
      });
    }
  },

  /**
   * 运动阈值参数变化处理
   */
  onMotionThresholdChange: function(page, value) {
    console.log('运动阈值参数变化:', value);
    
    // 更新页面数据
    page.setData({
      'dogVisionParams.motionThreshold': value
    });
    
    // 同步到视觉管理器
    const visionManager = require('./vision-manager');
    if (global.visionContext) {
      visionManager.updateVisionParams(global.visionContext, {
        motionThreshold: value
      });
    }
  },

  /**
   * 运动物体大小阈值参数变化处理
   */
  onMotionSizeThresholdChange: function(page, value) {
    console.log('运动物体大小阈值参数变化:', value);
    
    // 更新页面数据
    page.setData({
      'dogVisionParams.motionSizeThreshold': value
    });
    
    // 同步到视觉管理器
    const visionManager = require('./vision-manager');
    if (global.visionContext) {
      visionManager.updateVisionParams(global.visionContext, {
        motionSizeThreshold: value
      });
    }
  },

  /**
   * 重置视觉参数
   */
  resetVisionParams: function(page) {
    // 获取当前品种名称
    const breedName = page.data.breedName;
    const app = getApp();
    const breedType = app.globalData.breedType || 'dog';
    
    console.log('重置视觉参数，品种:', breedName, '类型:', breedType);
    
    // 重置为原始参数
    const originalParams = page.data.originalDogVisionParams;
    
    page.setData({
      dogVisionParams: {
        ...originalParams
      }
    });
    
    // 根据品种重新设置特定参数
    this.setBreedVisualAcuity(page, breedName, breedType);
    
    // 同步到视觉管理器
    const visionManager = require('./vision-manager');
    if (global.visionContext) {
      visionManager.updateVisionParams(global.visionContext, page.data.dogVisionParams);
    }
    
    wx.showToast({
      title: '参数已重置',
      icon: 'success',
      duration: 1500
    });
  }
};

module.exports = visionParamsManager;
