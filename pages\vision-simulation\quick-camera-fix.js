/**
 * 快速相机修复脚本
 * 可以在控制台直接调用的相机问题修复工具
 */

// 快速修复函数 - 可以在控制台直接调用
function quickCameraFix() {
  console.log('🔧 开始快速相机修复...');
  
  // 检查是否在小程序环境中
  if (typeof wx === 'undefined') {
    console.error('❌ 请在微信小程序环境中运行此脚本');
    return;
  }
  
  // 检查页面实例是否存在
  if (!global.page) {
    console.error('❌ 页面实例不存在，请确保在vision-simulation页面中运行');
    return;
  }
  
  const page = global.page;
  
  // 步骤1: 检查权限状态
  console.log('📋 步骤1: 检查相机权限状态...');
  
  wx.getSetting({
    success: (res) => {
      console.log('权限设置:', res.authSetting);
      
      const cameraPermission = res.authSetting['scope.camera'];
      
      if (cameraPermission === true) {
        console.log('✅ 相机权限已授权');
        // 权限正常，尝试重置相机
        resetAndRetryCamera(page);
      } else if (cameraPermission === false) {
        console.log('❌ 相机权限被拒绝');
        // 权限被拒绝，引导用户开启
        guidePermissionSetting();
      } else {
        console.log('❓ 相机权限未申请');
        // 权限未申请，尝试申请
        requestCameraPermission(page);
      }
    },
    fail: (error) => {
      console.error('❌ 获取权限设置失败:', error);
      showErrorMessage('获取权限设置失败，请重启小程序');
    }
  });
}

// 申请相机权限
function requestCameraPermission(page) {
  console.log('📋 步骤2: 申请相机权限...');
  
  wx.authorize({
    scope: 'scope.camera',
    success: () => {
      console.log('✅ 相机权限申请成功');
      showSuccessMessage('相机权限申请成功');
      
      // 权限申请成功，重新初始化相机
      setTimeout(() => {
        resetAndRetryCamera(page);
      }, 500);
    },
    fail: (error) => {
      console.error('❌ 相机权限申请失败:', error);
      
      if (error.errMsg && error.errMsg.includes('auth deny')) {
        console.log('用户拒绝了权限申请');
        guidePermissionSetting();
      } else {
        showErrorMessage('权限申请失败: ' + error.errMsg);
      }
    }
  });
}

// 引导用户手动设置权限
function guidePermissionSetting() {
  console.log('📋 步骤3: 引导用户手动设置权限...');
  
  wx.showModal({
    title: '需要相机权限',
    content: '爱宠视觉需要使用相机来模拟宠物视角，请点击"前往设置"开启相机权限',
    confirmText: '前往设置',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        wx.openSetting({
          success: (settingRes) => {
            console.log('设置页面返回:', settingRes.authSetting);
            
            if (settingRes.authSetting['scope.camera']) {
              console.log('✅ 用户已开启相机权限');
              showSuccessMessage('相机权限开启成功');
              
              // 权限开启成功，重新初始化相机
              setTimeout(() => {
                resetAndRetryCamera(global.page);
              }, 500);
            } else {
              console.log('❌ 用户未开启相机权限');
              showErrorMessage('相机权限未开启，无法使用视觉功能');
            }
          },
          fail: (error) => {
            console.error('❌ 打开设置页面失败:', error);
            showErrorMessage('打开设置页面失败');
          }
        });
      } else {
        console.log('用户取消了权限设置');
        showErrorMessage('用户取消了权限设置');
      }
    }
  });
}

// 重置并重试相机
function resetAndRetryCamera(page) {
  console.log('📋 步骤4: 重置并重试相机初始化...');
  
  // 停止现有相机
  if (page.cameraCtx) {
    try {
      page.cameraCtx.stop();
      console.log('✅ 已停止现有相机');
    } catch (error) {
      console.error('停止相机时出错:', error);
    }
    page.cameraCtx = null;
  }
  
  // 清理WebGL上下文
  if (global.webglContext) {
    try {
      console.log('🧹 清理WebGL资源...');
      // 这里可以添加WebGL清理逻辑
      global.webglContext = null;
      console.log('✅ WebGL资源已清理');
    } catch (error) {
      console.error('清理WebGL资源时出错:', error);
    }
  }
  
  // 重置页面状态
  page.setData({
    cameraLoading: true,
    cameraError: false,
    cameraErrorMsg: '',
    showCameraSettingBtn: false
  });
  
  console.log('✅ 页面状态已重置');
  
  // 延迟重新初始化相机
  setTimeout(() => {
    console.log('🚀 开始重新初始化相机...');
    
    try {
      page.initCamera();
      console.log('✅ 相机初始化已启动');
      showSuccessMessage('正在重新初始化相机...');
    } catch (error) {
      console.error('❌ 相机初始化失败:', error);
      showErrorMessage('相机初始化失败: ' + error.message);
    }
  }, 1000);
}

// 显示成功消息
function showSuccessMessage(message) {
  console.log('✅ ' + message);
  
  if (typeof wx !== 'undefined') {
    wx.showToast({
      title: message,
      icon: 'success',
      duration: 2000
    });
  }
}

// 显示错误消息
function showErrorMessage(message) {
  console.error('❌ ' + message);
  
  if (typeof wx !== 'undefined') {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
  }
}

// 检查设备兼容性
function checkDeviceCompatibility() {
  console.log('🔍 检查设备兼容性...');
  
  if (typeof wx === 'undefined') {
    console.error('❌ 不在微信小程序环境中');
    return false;
  }
  
  wx.getSystemInfo({
    success: (res) => {
      console.log('📱 设备信息:', res);
      console.log('系统:', res.system);
      console.log('微信版本:', res.version);
      console.log('基础库版本:', res.SDKVersion);
      
      // 检查相机权限状态
      if (res.cameraAuthorized === false) {
        console.warn('⚠️ 设备相机权限被系统拒绝');
      }
      
      // 检查WebGL支持
      try {
        const canvas = wx.createOffscreenCanvas({ type: 'webgl' });
        if (canvas) {
          console.log('✅ 设备支持WebGL');
        } else {
          console.warn('⚠️ 设备可能不支持WebGL');
        }
      } catch (error) {
        console.error('❌ WebGL检测失败:', error);
      }
    },
    fail: (error) => {
      console.error('❌ 获取设备信息失败:', error);
    }
  });
}

// 完整的诊断报告
function fullDiagnosis() {
  console.log('🔍 开始完整诊断...');
  console.log('==========================================');
  
  // 检查环境
  console.log('1. 环境检查:');
  console.log('   - 微信小程序环境:', typeof wx !== 'undefined' ? '✅' : '❌');
  console.log('   - 页面实例存在:', global.page ? '✅' : '❌');
  console.log('   - WebGL上下文:', global.webglContext ? '✅' : '❌');
  
  // 检查设备兼容性
  checkDeviceCompatibility();
  
  // 检查权限状态
  if (typeof wx !== 'undefined') {
    wx.getSetting({
      success: (res) => {
        console.log('2. 权限状态:');
        console.log('   - 相机权限:', res.authSetting['scope.camera'] === true ? '✅ 已授权' : 
                    res.authSetting['scope.camera'] === false ? '❌ 被拒绝' : '❓ 未申请');
        console.log('   - 相册权限:', res.authSetting['scope.writePhotosAlbum'] === true ? '✅ 已授权' : 
                    res.authSetting['scope.writePhotosAlbum'] === false ? '❌ 被拒绝' : '❓ 未申请');
      }
    });
  }
  
  console.log('==========================================');
  console.log('💡 如需修复相机问题，请运行: quickCameraFix()');
}

// 导出函数供控制台使用
if (typeof window !== 'undefined') {
  window.quickCameraFix = quickCameraFix;
  window.checkDeviceCompatibility = checkDeviceCompatibility;
  window.fullDiagnosis = fullDiagnosis;
}

// 导出模块
module.exports = {
  quickCameraFix,
  checkDeviceCompatibility,
  fullDiagnosis
};
