/**
 * UI事件处理器
 * 负责处理各种UI交互事件
 */

const uiController = require('./ui-controller');
const tipsManager = require('./tips-manager');

const uiEventHandler = {

  /**
   * 切换视角
   */
  toggleView: function(page) {
    uiController.toggleView(page);
  },

  /**
   * 触摸开始处理
   */
  handleTouchStart: function(page, e) {
    // 记录触摸开始时间
    page.touchStartTime = Date.now();
    page.touchStartX = e.touches[0].clientX;
  },

  /**
   * 触摸结束处理
   */
  handleTouchEnd: function(page, e) {
    // 如果是短按，切换视角
    if (Date.now() - page.touchStartTime < 300) {
      page.toggleView();
    }
    
    page.touchEndX = e.changedTouches[0].clientX;
    
    // 检测滑动手势
    const deltaX = page.touchEndX - page.touchStartX;
    const threshold = 50; // 滑动阈值
    
    if (Math.abs(deltaX) > threshold) {
      if (deltaX > 0) {
        // 向右滑动
        console.log('向右滑动');
      } else {
        // 向左滑动
        console.log('向左滑动');
      }
    }
  },

  /**
   * 视图区域点击处理
   */
  handleViewAreaTap: function(page) {
    const currentTime = new Date().getTime();
    const timeDiff = currentTime - page.lastTapTime;
    
    if (timeDiff < 300) {
      // 双击事件
      page.toggleControlPanel();
    } else {
      // 单击事件
      page.toggleView();
    }
    
    page.lastTapTime = currentTime;
  },

  /**
   * 切换分析区域展开状态
   */
  toggleAnalysisExpand: function(page) {
    const newState = !page.data.isAnalysisExpanded;
    page.setData({
      isAnalysisExpanded: newState
    });
    
    // 触觉反馈
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
  },

  /**
   * 切换控制面板显示状态
   */
  toggleControlPanel: function(page) {
    const newState = !page.data.showControls;
    page.setData({
      showControls: newState
    });
    
    // 触觉反馈
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
  },

  /**
   * 切换相机选择器
   */
  toggleCameraSelector: function(page) {
    page.setData({
      showCameraSelector: !page.data.showCameraSelector
    });
  },

  /**
   * 切换视觉参数面板
   */
  toggleVisionParams: function(page) {
    // 在显示参数面板前，确保同步最新的品种特定参数
    if (!page.data.showVisionParams) {
      // 获取当前品种信息
      const breedName = page.data.breedName;
      const app = getApp();
      const breedType = app.globalData.breedType || 'dog';
      
      // 确保参数与当前品种匹配
      const visionParamsManager = require('./vision-params-manager');
      visionParamsManager.setBreedVisualAcuity(page, breedName, breedType);
    }
    
    page.setData({
      showVisionParams: !page.data.showVisionParams
    });
  },

  /**
   * 切换分辨率选择器
   */
  toggleResolutionSelector: function(page) {
    page.setData({
      showResolutionSelector: !page.data.showResolutionSelector
    });
  },

  /**
   * 切换视觉模式选择器
   */
  toggleVisionModeSelector: function(page) {
    const newState = !page.data.showVisionModeSelector;
    
    page.setData({
      showVisionModeSelector: newState,
      showVisionModeDetail: newState // 同时控制详情显示
    });
  },

  /**
   * 阻止触摸移动
   */
  preventTouchMove: function(e) {
    // 阻止触摸事件传播
    return false;
  },

  /**
   * 选择视觉模式
   */
  selectVisionMode: function(page, mode) {
    console.log('选择视觉模式:', mode);
    
    // 更新当前模式
    page.setData({
      currentVisionMode: mode,
      showVisionModeSelector: false
    });
    
    // 根据模式启用/禁用相应功能
    const features = { ...page.data.features };
    
    switch(mode) {
      case 'dichromatic':
        features.color = true;
        features.night = false;
        features.motion = false;
        break;
      case 'acuity':
        features.color = true;
        features.night = false;
        features.motion = false;
        break;
      case 'nightVision':
        features.color = true;
        features.night = true;
        features.motion = false;
        break;
      case 'motionVision':
        features.color = true;
        features.night = true;
        features.motion = true;
        break;
    }
    
    page.setData({
      features: features
    });
    
    // 同步到视觉管理器
    const visionManager = require('./vision-manager');
    if (global.visionContext) {
      visionManager.updateVisionFeatures(global.visionContext, features);
    }
    
    // 显示切换提示
    wx.showToast({
      title: `已切换到${this.getModeName(mode)}`,
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 获取模式名称
   */
  getModeName: function(mode) {
    switch(mode) {
      case 'dichromatic': return '二色视觉';
      case 'acuity': return '视力辨析';
      case 'nightVision': return '光感模式';
      case 'motionVision': return '运动视觉';
      default: return '未知模式';
    }
  },

  /**
   * 切换运动视觉
   */
  toggleMotionVision: function(page) {
    // 切换运动视觉状态
    const newMotionState = !page.data.features.motion;
    
    page.setData({
      'features.motion': newMotionState
    });
    
    // 同步到视觉管理器
    const visionManager = require('./vision-manager');
    if (global.visionContext) {
      visionManager.updateVisionFeatures(global.visionContext, {
        ...page.data.features,
        motion: newMotionState
      });
    }
    
    // 显示提示
    if (newMotionState) {
      page.showMotionTip();
    }
    
    wx.showToast({
      title: newMotionState ? '运动视觉已启用' : '运动视觉已关闭',
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 显示运动视觉提示
   */
  showMotionTip: function(page) {
    tipsManager.showMotionVisionTip(page);
  },

  /**
   * 关闭运动视觉提示
   */
  closeMotionTip: function(page) {
    tipsManager.closeMotionVisionTip(page);
  },

  /**
   * 显示强光适应提示
   */
  showBrightLightTip: function(page) {
    tipsManager.showBrightLightTip(page);
  },

  /**
   * 关闭强光适应提示
   */
  closeBrightLightTip: function(page) {
    tipsManager.closeBrightLightTip(page);
  },

  /**
   * 显示夜视模式提示
   */
  showNightVisionTip: function(page) {
    tipsManager.showNightVisionTip(page);
  },

  /**
   * 关闭夜视模式提示
   */
  closeNightVisionTip: function(page) {
    tipsManager.closeNightVisionTip(page);
  },

  /**
   * 标签页切换
   */
  changeTab: function(page, index) {
    uiController.changeTab(index, page);
  },

  /**
   * 滑动切换标签页
   */
  swiperChange: function(page, e) {
    uiController.swiperChange(e, page);
  },

  /**
   * 阻止事件冒泡
   */
  preventBubble: function(e) {
    return false;
  },

  /**
   * 导航返回
   */
  navigateBack: function() {
    wx.navigateBack({
      delta: 1
    });
  }
};

module.exports = uiEventHandler;
