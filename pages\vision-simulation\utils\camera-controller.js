/**
 * 相机控制管理器
 * 负责相机的初始化、切换、错误处理等功能
 */

const utils = require('./utils');
const camManager = require('./cam-manager');

const cameraController = {

  /**
   * 初始化相机类型
   */
  initializeCameraType: function(page) {
    // 根据当前的camMode和cameraPosition设置currentCameraType
    let currentCameraType;
    
    if (page.data.camMode) {
      currentCameraType = 'cam';
    } else {
      currentCameraType = page.data.cameraPosition; // 'back' 或 'front'
    }
    
    page.setData({
      currentCameraType: currentCameraType
    });
    
    console.log('初始化相机类型:', currentCameraType);
  },

  /**
   * 初始化相机
   */
  initCamera: function(page) {
    // CAM模式下不初始化手机相机
    if (page.data.camMode) {
      console.log('CAM模式下，跳过手机相机初始化');
      return;
    }

    console.log('开始初始化相机');

    // 设置加载状态
    page.setData({
      cameraLoading: true,
      cameraError: false,
      cameraErrorMsg: ''
    });

    // 使用camera-manager的initCamera方法
    const cameraManager = require('./camera-manager');

    cameraManager.initCamera(page)
      .then(cameraContext => {
        if (cameraContext) {
          page.cameraCtx = cameraContext;
          console.log('相机初始化成功');

          page.setData({
            cameraLoading: false,
            cameraError: false
          });

          // 初始化WebGL
          this.initWebGL(page);
        } else {
          throw new Error('相机上下文为空');
        }
      })
      .catch(error => {
        console.error('相机初始化失败:', error);
        page.handleCameraError(error);
      });
  },

  /**
   * 初始化WebGL
   */
  initWebGL: function(page) {
    console.log('开始初始化WebGL');
    
    const webglRenderer = require('./webgl-renderer');
    
    try {
      // 初始化WebGL上下文
      webglRenderer.initWebGL('webglCanvas', page, (context) => {
        if (context) {
          global.webglContext = context;
          console.log('WebGL初始化成功');
          
          // 设置帧回调
          if (page.cameraCtx) {
            page.cameraCtx.onCameraFrame((frame) => {
              page.processFrameWebGL(frame);
            });
          }
        } else {
          console.error('WebGL初始化失败');
          page.setData({
            cameraError: true,
            cameraErrorMsg: 'WebGL初始化失败，请检查设备兼容性'
          });
        }
      });
    } catch (error) {
      console.error('WebGL初始化出错:', error);
      page.setData({
        cameraError: true,
        cameraErrorMsg: 'WebGL初始化出错: ' + error.message
      });
    }
  },

  /**
   * 处理相机错误
   */
  handleCameraError: function(page, error) {
    console.log('处理相机错误:', error);
    utils.handleCameraError(error, page);

    // 如果是权限问题，提供智能修复选项
    if (error && (error.errCode === 'auth_denied' || error.errCode === 'auth_rejected')) {
      setTimeout(() => {
        wx.showModal({
          title: '相机权限问题',
          content: '检测到相机权限问题，是否尝试自动修复？',
          confirmText: '自动修复',
          cancelText: '手动处理',
          success: (res) => {
            if (res.confirm) {
              page.smartCameraFix();
            }
          }
        });
      }, 1000);
    }
  },

  /**
   * 重新初始化相机
   */
  retryCameraInit: function(page) {
    console.log('重新初始化相机');
    
    // 清除任何现有的定时器
    if (page._retryTimer) {
      clearTimeout(page._retryTimer);
      page._retryTimer = null;
    }
    
    // 重置相机状态
    this.resetCameraState(page);
    
    // 延迟重新初始化，给系统一些时间
    page._retryTimer = setTimeout(() => {
      this.initCamera(page);
    }, 1000);
  },

  /**
   * 重置相机状态
   */
  resetCameraState: function(page) {
    console.log('重置相机状态');

    if (page.cameraCtx) {
      try {
        const cameraManager = require('./camera-manager');
        cameraManager.stopCamera(page.cameraCtx);
      } catch (error) {
        console.error('停止相机时出错:', error);
      }
      page.cameraCtx = null;
    }

    page.setData({
      cameraLoading: false,
      cameraError: false,
      cameraErrorMsg: ''
    });
  },

  /**
   * 切换相机类型
   */
  selectCameraType: function(page, cameraType) {
    console.log('选择相机类型:', cameraType);
    
    // 关闭选择器
    page.setData({
      showCameraSelector: false
    });
    
    // 如果选择的是当前类型，直接返回
    if (cameraType === page.data.currentCameraType) {
      console.log('已经是当前相机类型，无需切换');
      return;
    }
    
    // 执行切换
    this.switchToCamera(page, cameraType);
  },

  /**
   * 切换到指定相机
   */
  switchToCamera: function(page, cameraType) {
    switch(cameraType) {
      case 'back':
      case 'front':
        this.switchToPhoneCamera(page, cameraType);
        break;
      case 'cam':
        this.switchToHardwareCamera(page);
        break;
      default:
        console.error('未知的相机类型:', cameraType);
    }
  },

  /**
   * 切换到手机相机
   */
  switchToPhoneCamera: function(page, position) {
    // 如果当前是CAM模式，先关闭CAM
    if (page.data.camMode) {
      console.log('从CAM模式切换到手机相机');
      page.disableCamMode();
    }
    
    // 更新相机位置和类型
    page.setData({
      cameraPosition: position,
      currentCameraType: position
    });
    
    console.log('切换到手机相机:', position);
    
    // 重新初始化相机
    this.reinitializeCamera(page);
  },

  /**
   * 切换到硬件相机
   */
  switchToHardwareCamera: function(page) {
    console.log('切换到硬件相机');
    page.enableCamMode();
  },

  /**
   * 重新初始化相机
   */
  reinitializeCamera: function(page) {
    // 先停止现有相机
    if (page.cameraCtx) {
      try {
        const cameraManager = require('./camera-manager');
        cameraManager.stopCamera(page.cameraCtx);
      } catch (error) {
        console.error('停止相机时出错:', error);
      }
      page.cameraCtx = null;
    }

    // 清理WebGL上下文
    if (global.webglContext) {
      try {
        const webglRenderer = require('./webgl-renderer');
        webglRenderer.cleanup(global.webglContext);
        global.webglContext = null;
      } catch (error) {
        console.error('清理WebGL时出错:', error);
      }
    }

    // 重新初始化
    setTimeout(() => {
      this.initCamera(page);
    }, 500);
  },

  /**
   * 改变分辨率
   */
  changeResolution: function(page, index) {
    const currentOption = page.data.resolutionOptions[index];
    
    console.log('切换分辨率到:', currentOption.name);
    
    page.setData({
      currentResolutionIndex: index,
      showResolutionSelector: false
    });
    
    // 重新初始化相机以应用新分辨率
    this.reinitializeCamera(page);
  },

  /**
   * 打开相机设置
   */
  openCameraSetting: function(page) {
    console.log('用户点击打开设置按钮');
    utils.openCameraSetting(page);
  }
};

module.exports = cameraController;
