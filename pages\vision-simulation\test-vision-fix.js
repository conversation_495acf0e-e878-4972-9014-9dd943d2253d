/**
 * 视觉模拟修复测试脚本
 * 测试WebGL初始化和视觉管理器的修复
 */

console.log('🔧 开始测试视觉模拟修复...');

// 模拟微信小程序环境
global.wx = {
  createSelectorQuery: () => ({
    select: (selector) => ({
      fields: (options) => ({
        exec: (callback) => {
          console.log('📋 模拟Canvas节点查询:', selector);
          // 模拟Canvas节点
          const mockCanvas = {
            getContext: (type) => {
              if (type === 'webgl') {
                console.log('✅ 模拟WebGL上下文创建成功');
                return {
                  createShader: () => ({}),
                  shaderSource: () => {},
                  compileShader: () => {},
                  getShaderParameter: () => true,
                  createProgram: () => ({}),
                  attachShader: () => {},
                  linkProgram: () => {},
                  getProgramParameter: () => true,
                  createBuffer: () => ({}),
                  bindBuffer: () => {},
                  bufferData: () => {},
                  createTexture: () => ({}),
                  bindTexture: () => {},
                  texParameteri: () => {},
                  texImage2D: () => {},
                  viewport: () => {},
                  useProgram: () => {},
                  getAttribLocation: () => 0,
                  getUniformLocation: () => ({}),
                  enableVertexAttribArray: () => {},
                  vertexAttribPointer: () => {},
                  uniform1f: () => {},
                  uniform1i: () => {},
                  uniform2f: () => {},
                  activeTexture: () => {},
                  drawArrays: () => {},
                  VERTEX_SHADER: 35633,
                  FRAGMENT_SHADER: 35632,
                  COMPILE_STATUS: 35713,
                  LINK_STATUS: 35714,
                  ARRAY_BUFFER: 34962,
                  STATIC_DRAW: 35044,
                  TEXTURE_2D: 3553,
                  RGBA: 6408,
                  UNSIGNED_BYTE: 5121,
                  CLAMP_TO_EDGE: 33071,
                  LINEAR: 9729,
                  TEXTURE0: 33984,
                  TEXTURE1: 33985,
                  TEXTURE_WRAP_S: 10242,
                  TEXTURE_WRAP_T: 10243,
                  TEXTURE_MIN_FILTER: 10241,
                  TEXTURE_MAG_FILTER: 10240,
                  TRIANGLES: 4,
                  FLOAT: 5126
                };
              }
              return null;
            },
            width: 1280,
            height: 720
          };
          
          setTimeout(() => {
            callback([{
              node: mockCanvas,
              width: 1280,
              height: 720
            }]);
          }, 50);
        }
      })
    })
  }),
  createCameraContext: () => ({
    onCameraFrame: (callback) => ({
      start: () => {
        console.log('✅ 模拟相机帧监听器启动');
        // 模拟帧数据
        setTimeout(() => {
          callback({
            data: new ArrayBuffer(1280 * 720 * 4),
            width: 1280,
            height: 720
          });
        }, 100);
        return true;
      },
      stop: () => console.log('✅ 模拟相机帧监听器停止')
    }),
    stop: () => console.log('✅ 模拟相机停止')
  }),
  getSystemInfo: (options) => {
    setTimeout(() => options.success({
      platform: 'devtools',
      system: 'Windows 10'
    }), 10);
  },
  showToast: (options) => console.log('📱 提示:', options.title)
};

// 模拟页面实例
global.page = {
  data: {
    cameraLoading: false,
    cameraError: false,
    cameraErrorMsg: '',
    camMode: false,
    cameraPosition: 'back',
    cameraInitializing: false,
    features: {
      color: true,
      night: true,
      motion: false,
      isCat: false
    },
    dogVisionParams: {
      resolutionFactor: 0.5,
      antiAliasFactor: 0.3,
      brightness: 1.5,
      contrast: 1.2,
      motionSensitivity: 0.8,
      motionThreshold: 0.1,
      motionSizeThreshold: 0.05
    },
    resolutionOptions: [
      { name: '标清', width: 1280, height: 720 }
    ],
    currentResolutionIndex: 0,
    currentView: 'dog',
    devicePerformance: 'medium',
    LOW_LIGHT_THRESHOLD: 50,
    HIGH_LIGHT_THRESHOLD: 200
  },
  setData: (data) => {
    console.log('📊 页面状态更新:', Object.keys(data).join(', '));
    Object.assign(global.page.data, data);
  },
  cameraCtx: null,
  processFrameWebGL: (frame) => {
    console.log('🎬 处理相机帧:', frame ? '有数据' : '无数据');
  },
  initCamera: () => {
    console.log('📷 初始化相机方法被调用');
  },
  _lastFrameCheckTime: Date.now(),
  _lastFrame: null
};

// 初始化全局变量
global.webglContext = null;
global.visionContext = null;

// 测试函数
async function testVisionFix() {
  try {
    console.log('\n=== 测试1: WebGL渲染器初始化 ===');
    const webglRenderer = require('./utils/webgl-renderer');
    
    const webglContext = await webglRenderer.initWebGL('processCanvas');
    if (webglContext) {
      console.log('✅ WebGL渲染器初始化成功');
      global.webglContext = webglContext;
    } else {
      throw new Error('WebGL渲染器初始化失败');
    }
    
    console.log('\n=== 测试2: 视觉管理器初始化 ===');
    const visionManager = require('./utils/vision-manager');
    
    const visionContext = await visionManager.initVisionManager({
      animalType: 'dog',
      isLowLight: false,
      visualAcuity: {
        RESOLUTION_FACTOR: 0.5,
        VIEW_FIELD_FACTOR: 0.3,
        ENABLED: true
      },
      nightVision: {
        ENABLED: true
      },
      dichromaticVision: {
        ENABLED: true
      }
    });
    
    if (visionContext && visionContext.webglContext) {
      console.log('✅ 视觉管理器初始化成功');
      global.visionContext = visionContext;
    } else {
      throw new Error('视觉管理器初始化失败');
    }
    
    console.log('\n=== 测试3: 帧处理测试 ===');
    const mockFrame = {
      data: new ArrayBuffer(1280 * 720 * 4),
      width: 1280,
      height: 720
    };
    
    const processResult = visionManager.processFrame(
      mockFrame,
      global.webglContext,
      'dog',
      {
        enabled: false,
        sensitivity: 0.8,
        threshold: 0.1,
        sizeThreshold: 0.05
      }
    );
    
    if (processResult) {
      console.log('✅ 帧处理测试成功');
      console.log('📊 处理结果:', {
        brightness: processResult.brightness,
        isLowLight: processResult.isLowLight,
        isBrightLight: processResult.isBrightLight
      });
    } else {
      throw new Error('帧处理测试失败');
    }
    
    console.log('\n🎉 所有测试通过！视觉模拟修复成功！');
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    console.error('详细错误:', error);
  }
}

// 运行测试
testVisionFix();
