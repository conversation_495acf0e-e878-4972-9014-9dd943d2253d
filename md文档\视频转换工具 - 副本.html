<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宠物视角视频转换工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #4a5568;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            padding-left: 10px;
        }

        .upload-zone {
            border: 3px dashed #cbd5e0;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f7fafc;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-zone:hover {
            border-color: #667eea;
            background: #edf2f7;
        }

        .upload-zone.dragover {
            border-color: #667eea;
            background: #e6fffa;
        }

        .file-input {
            display: none;
        }

        .upload-icon {
            font-size: 3em;
            color: #a0aec0;
            margin-bottom: 15px;
        }

        .species-selector {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .species-card {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .species-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .species-card.selected {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .species-icon {
            font-size: 3em;
            margin-bottom: 10px;
        }

        .breed-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .breed-card {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .breed-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .breed-card.selected {
            border-color: #667eea;
            background: #f0f4ff;
            border-width: 2px;
        }

        .breed-icon {
            font-size: 2em;
            margin-bottom: 8px;
        }

        .breed-name {
            font-size: 0.9em;
            font-weight: 500;
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .control-group {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }

        .control-label {
            font-weight: 500;
            margin-bottom: 10px;
            color: #4a5568;
        }

        .slider-container {
            margin-bottom: 15px;
        }

        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #e2e8f0;
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }

        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
        }

        .value-display {
            text-align: center;
            font-size: 0.9em;
            color: #718096;
            margin-top: 5px;
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
        }

        .processing-section {
            text-align: center;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }

        .progress-container {
            margin: 20px 0;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            color: #4a5568;
        }

        .video-preview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }

        .video-container {
            text-align: center;
        }

        .video-container h3 {
            margin-bottom: 15px;
            color: #4a5568;
        }

        video {
            width: 100%;
            max-width: 400px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        canvas {
            display: none;
        }

        .download-section {
            text-align: center;
            margin-top: 30px;
            display: none;
        }

        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }

        .status-success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .status-error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #fc8181;
        }

        .status-info {
            background: #bee3f8;
            color: #2a4365;
            border: 1px solid #63b3ed;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .main-panel {
                padding: 20px;
            }
            
            .species-selector {
                grid-template-columns: 1fr;
            }
            
            .video-preview {
                grid-template-columns: 1fr;
            }
            
            .controls {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐕🐱 宠物视角视频转换工具</h1>
            <p>上传视频文件，选择宠物品种，生成模拟宠物视觉的视频</p>
        </div>

        <div class="main-panel">
            <!-- 视频上传区域 -->
            <div class="section">
                <div class="section-title">📁 上传视频文件</div>
                <div class="upload-zone" id="uploadZone">
                    <div class="upload-icon">🎬</div>
                    <p><strong>点击选择视频文件</strong> 或拖拽文件到此处</p>
                    <p>支持 MP4, MOV, AVI, WebM 格式</p>
                    <input type="file" id="videoInput" class="file-input" accept="video/*">
                </div>
            </div>

            <!-- 动物类型选择 -->
            <div class="section">
                <div class="section-title">🐾 选择动物类型</div>
                <div class="species-selector">
                    <div class="species-card" data-species="dog">
                        <div class="species-icon">🐕</div>
                        <h3>犬科动物</h3>
                        <p>狗狗视角模拟</p>
                    </div>
                    <div class="species-card" data-species="cat">
                        <div class="species-icon">🐱</div>
                        <h3>猫科动物</h3>
                        <p>猫咪视角模拟</p>
                    </div>
                </div>
            </div>

            <!-- 品种选择 -->
            <div class="section">
                <div class="section-title">🎯 选择具体品种</div>
                <div id="breedSelector" style="display: none;">
                    <div id="dogBreeds" class="breed-grid" style="display: none;">
                        <div class="breed-card" data-breed="拉布拉多">
                            <div class="breed-icon">🦮</div>
                            <div class="breed-name">拉布拉多</div>
                        </div>
                        <div class="breed-card" data-breed="金毛寻回">
                            <div class="breed-icon">🐕‍🦺</div>
                            <div class="breed-name">金毛寻回</div>
                        </div>
                        <div class="breed-card" data-breed="哈士奇">
                            <div class="breed-icon">🐺</div>
                            <div class="breed-name">哈士奇</div>
                        </div>
                        <div class="breed-card" data-breed="边境牧羊">
                            <div class="breed-icon">🐩</div>
                            <div class="breed-name">边境牧羊</div>
                        </div>
                        <div class="breed-card" data-breed="贵宾(泰迪)">
                            <div class="breed-icon">🐕</div>
                            <div class="breed-name">贵宾(泰迪)</div>
                        </div>
                        <div class="breed-card" data-breed="柯基">
                            <div class="breed-icon">🐶</div>
                            <div class="breed-name">柯基</div>
                        </div>
                        <div class="breed-card" data-breed="吉娃娃">
                            <div class="breed-icon">🐕</div>
                            <div class="breed-name">吉娃娃</div>
                        </div>
                        <div class="breed-card" data-breed="法国斗牛">
                            <div class="breed-icon">🐶</div>
                            <div class="breed-name">法国斗牛</div>
                        </div>
                        <div class="breed-card" data-breed="比熊">
                            <div class="breed-icon">🦮</div>
                            <div class="breed-name">比熊</div>
                        </div>
                    </div>
                    <div id="catBreeds" class="breed-grid" style="display: none;">
                        <div class="breed-card" data-breed="狸花猫">
                            <div class="breed-icon">😺</div>
                            <div class="breed-name">狸花猫</div>
                        </div>
                        <div class="breed-card" data-breed="黄狸猫">
                            <div class="breed-icon">😸</div>
                            <div class="breed-name">黄狸猫</div>
                        </div>
                        <div class="breed-card" data-breed="玄猫">
                            <div class="breed-icon">🐈</div>
                            <div class="breed-name">玄猫</div>
                        </div>
                        <div class="breed-card" data-breed="三花猫">
                            <div class="breed-icon">😽</div>
                            <div class="breed-name">三花猫</div>
                        </div>
                        <div class="breed-card" data-breed="奶牛猫">
                            <div class="breed-icon">😺</div>
                            <div class="breed-name">奶牛猫</div>
                        </div>
                        <div class="breed-card" data-breed="英国短毛">
                            <div class="breed-icon">😼</div>
                            <div class="breed-name">英国短毛</div>
                        </div>
                        <div class="breed-card" data-breed="美国短毛">
                            <div class="breed-icon">🐈</div>
                            <div class="breed-name">美国短毛</div>
                        </div>
                        <div class="breed-card" data-breed="布偶">
                            <div class="breed-icon">😻</div>
                            <div class="breed-name">布偶</div>
                        </div>
                        <div class="breed-card" data-breed="暹罗">
                            <div class="breed-icon">🐱</div>
                            <div class="breed-name">暹罗</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视觉效果控制 -->
            <div class="section" id="controlsSection" style="display: none;">
                <div class="section-title">⚙️ 视觉效果控制</div>
                <div class="controls">
                    <div class="control-group">
                        <div class="control-label">🔧 基础参数</div>
                        <div class="slider-container">
                            <label>亮度增强</label>
                            <input type="range" class="slider" id="brightnessSlider" min="0.5" max="3.0" step="0.1" value="1.5">
                            <div class="value-display" id="brightnessValue">1.5</div>
                        </div>
                        <div class="slider-container">
                            <label>对比度</label>
                            <input type="range" class="slider" id="contrastSlider" min="0.5" max="2.5" step="0.1" value="1.3">
                            <div class="value-display" id="contrastValue">1.3</div>
                        </div>
                        <div class="slider-container">
                            <label>视力清晰度</label>
                            <input type="range" class="slider" id="resolutionSlider" min="0.1" max="1.0" step="0.05" value="0.5">
                            <div class="value-display" id="resolutionValue">0.5</div>
                        </div>
                    </div>
                    
                    <div class="control-group">
                        <div class="control-label">👁️ 视觉特效</div>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="colorVision" class="checkbox" checked>
                                <label for="colorVision">二色视觉 (核心效果)</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="nightVision" class="checkbox">
                                <label for="nightVision">夜视增强</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="motionVision" class="checkbox">
                                <label for="motionVision">运动感知增强</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="peripheralDim" class="checkbox">
                                <label for="peripheralDim">周边视野暗化</label>
                            </div>
                        </div>
                        
                        <!-- 二色视觉强度控制 -->
                        <div class="slider-container" id="colorVisionIntensity" style="margin-top: 15px;">
                            <label>二色视觉强度</label>
                            <input type="range" class="slider" id="colorIntensitySlider" min="0.3" max="1.0" step="0.1" value="0.8">
                            <div class="value-display" id="colorIntensityValue">0.8</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 状态消息 -->
            <div id="statusMessage" class="status-message"></div>

            <!-- 性能设置 -->
            <div class="section" id="performanceSection" style="display: none;">
                <div class="section-title">⚡ 性能设置</div>
                <div class="controls">
                    <div class="control-group">
                        <div class="control-label">目标帧率</div>
                        <div class="slider-container">
                            <input type="range" class="slider" id="frameRateSlider" min="15" max="30" step="5" value="30">
                            <div class="value-display" id="frameRateValue">30 fps</div>
                        </div>
                    </div>
                    <div class="control-group">
                        <div class="control-label">处理质量</div>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="highQuality" class="checkbox" checked>
                                <label for="highQuality">高质量处理</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="adaptiveFrameRate" class="checkbox" checked>
                                <label for="adaptiveFrameRate">自适应帧率</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 性能监控 -->
                <div id="performanceMonitor" style="display: none; margin-top: 15px; padding: 15px; background: #f0f4ff; border-radius: 8px;">
                    <h4 style="margin: 0 0 10px 0; color: #4a5568;">📊 实时性能监控</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 0.9em;">
                        <div>当前帧率: <span id="currentFps">--</span> fps</div>
                        <div>平均帧时间: <span id="avgFrameTime">--</span> ms</div>
                        <div>已处理帧数: <span id="processedFrames">--</span></div>
                        <div>掉帧率: <span id="dropRate">--</span>%</div>
                    </div>
                </div>
            </div>

            <!-- 处理按钮 -->
            <div class="processing-section">
                <button id="processBtn" class="btn" disabled>🚀 开始转换</button>
                <div class="progress-container" id="progressContainer">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">处理中...</div>
                </div>
            </div>

            <!-- 实时预览 -->
            <div class="section" id="previewSection" style="display: none;">
                <div class="section-title">👀 实时预览</div>
                <div style="text-align: center; margin-bottom: 20px;">
                    <button id="previewBtn" class="btn" style="background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);">
                        🔍 预览效果
                    </button>
                    <p style="margin-top: 10px; font-size: 0.9em; color: #718096;">
                        调整参数后点击预览，查看当前帧的处理效果
                    </p>
                </div>
                <div class="video-preview" id="livePreview" style="display: none;">
                    <div class="video-container">
                        <h3>📹 原始画面</h3>
                        <canvas id="originalCanvas" width="320" height="240" style="border-radius: 8px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);"></canvas>
                    </div>
                    <div class="video-container">
                        <h3>🎭 宠物视角预览</h3>
                        <canvas id="previewCanvas" width="320" height="240" style="border-radius: 8px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);"></canvas>
                    </div>
                </div>
            </div>

            <!-- 视频预览 -->
            <div class="video-preview" id="videoPreview" style="display: none;">
                <div class="video-container">
                    <h3>📹 原始视频</h3>
                    <video id="originalVideo" controls></video>
                </div>
                <div class="video-container">
                    <h3>🎭 宠物视角</h3>
                    <video id="processedVideo" controls></video>
                </div>
            </div>

            <!-- 下载区域 -->
            <div class="download-section" id="downloadSection">
                <button id="downloadBtn" class="btn">💾 下载转换后的视频</button>
            </div>
        </div>
    </div>

    <!-- Canvas 用于视频处理 -->
    <canvas id="processCanvas"></canvas>

    <script>
        // 视觉配置参数 (基于业务流程)
        const VISION_CONFIG = {
            // 犬科动物配置
            DOG: {
                BASE: {
                    BRIGHTNESS: 1.5,
                    CONTRAST: 1.3,
                    RESOLUTION_FACTOR: 0.5,
                    VIEW_FIELD_FACTOR: 0.3
                },
                // 特殊品种配置
                BREEDS: {
                    '哈士奇': {
                        BRIGHTNESS: 1.8,
                        CONTRAST: 1.3,
                        RESOLUTION_FACTOR: 0.27
                    },
                    '边境牧羊': {
                        BRIGHTNESS: 1.5,
                        CONTRAST: 1.3,
                        RESOLUTION_FACTOR: 0.4  // 相当于人类的2/5
                    },
                    '拉布拉多': {
                        BRIGHTNESS: 1.5,
                        CONTRAST: 1.3,
                        RESOLUTION_FACTOR: 1.0  // 相当于人类的20/20
                    },
                    '金毛寻回': {
                        BRIGHTNESS: 1.5,
                        CONTRAST: 1.3,
                        RESOLUTION_FACTOR: 0.33  // 相当于人类的1/3
                    }
                },
                COLOR_TRANSFORM: {
                    RED_FACTOR: 0.5,
                    GREEN_FACTOR: 0.5,
                    BLUE_PRESERVE: true
                }
            },
            // 猫科动物配置
            CAT: {
                BASE: {
                    BRIGHTNESS: 2.0,
                    CONTRAST: 1.5,
                    RESOLUTION_FACTOR: 0.2,
                    VIEW_FIELD_FACTOR: 0.3
                },
                // 特殊品种配置
                BREEDS: {
                    '暹罗': {
                        BRIGHTNESS: 1.5,  // 暹罗猫特殊参数
                        CONTRAST: 1.3,
                        RESOLUTION_FACTOR: 0.15
                    }
                },
                COLOR_TRANSFORM: {
                    RED_FACTOR: { r: 0.1, g: 0.2, b: 0.1 },
                    GREEN_FACTOR: { r: 0.2, g: 0.6, b: 0.2 },
                    BLUE_FACTOR: { r: 0.1, g: 0.2, b: 0.7 }
                }
            }
        };

        // 全局变量
        let selectedSpecies = null;
        let selectedBreed = null;
        let originalVideo = null;
        let isProcessing = false;
        // 添加性能优化相关变量
        let processingStats = {
            frameCount: 0,
            totalProcessingTime: 0,
            averageFrameTime: 0,
            droppedFrames: 0
        };
        let lastFrameTime = 0;
        let targetFrameRate = 30;
        let adaptiveFrameRate = 30;
        
        // 预分配内存缓冲区
        let imageDataBuffer = null;
        let processedImageData = null;

        // DOM 元素
        const uploadZone = document.getElementById('uploadZone');
        const videoInput = document.getElementById('videoInput');
        const speciesCards = document.querySelectorAll('.species-card');
        const breedSelector = document.getElementById('breedSelector');
        const dogBreeds = document.getElementById('dogBreeds');
        const catBreeds = document.getElementById('catBreeds');
        const controlsSection = document.getElementById('controlsSection');
        const processBtn = document.getElementById('processBtn');
        const progressContainer = document.getElementById('progressContainer');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const videoPreview = document.getElementById('videoPreview');
        const originalVideoEl = document.getElementById('originalVideo');
        const processedVideoEl = document.getElementById('processedVideo');
        const downloadSection = document.getElementById('downloadSection');
        const downloadBtn = document.getElementById('downloadBtn');
        const statusMessage = document.getElementById('statusMessage');
        const processCanvas = document.getElementById('processCanvas');

        // 滑块元素
        const brightnessSlider = document.getElementById('brightnessSlider');
        const contrastSlider = document.getElementById('contrastSlider');
        const resolutionSlider = document.getElementById('resolutionSlider');
        const brightnessValue = document.getElementById('brightnessValue');
        const contrastValue = document.getElementById('contrastValue');
        const resolutionValue = document.getElementById('resolutionValue');

        // 复选框元素
        const colorVisionCheck = document.getElementById('colorVision');
        const nightVisionCheck = document.getElementById('nightVision');
        const motionVisionCheck = document.getElementById('motionVision');
        const peripheralDimCheck = document.getElementById('peripheralDim');
        
        // 强度控制滑块
        const colorIntensitySlider = document.getElementById('colorIntensitySlider');
        const colorIntensityValue = document.getElementById('colorIntensityValue');
        
        // 性能相关元素
        const performanceSection = document.getElementById('performanceSection');
        const frameRateSlider = document.getElementById('frameRateSlider');
        const frameRateValue = document.getElementById('frameRateValue');
        const highQualityCheck = document.getElementById('highQuality');
        const adaptiveFrameRateCheck = document.getElementById('adaptiveFrameRate');
        const performanceMonitor = document.getElementById('performanceMonitor');
        
        // 预览相关元素
        const previewSection = document.getElementById('previewSection');
        const previewBtn = document.getElementById('previewBtn');
        const livePreview = document.getElementById('livePreview');
        const originalCanvas = document.getElementById('originalCanvas');
        const previewCanvas = document.getElementById('previewCanvas');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initEventListeners();
            updateSliderValues();
        });

        // 初始化事件监听器
        function initEventListeners() {
            // 文件上传
            uploadZone.addEventListener('click', () => videoInput.click());
            uploadZone.addEventListener('dragover', handleDragOver);
            uploadZone.addEventListener('drop', handleDrop);
            videoInput.addEventListener('change', handleFileSelect);

            // 动物类型选择
            speciesCards.forEach(card => {
                card.addEventListener('click', () => selectSpecies(card.dataset.species));
            });

            // 品种选择
            document.querySelectorAll('.breed-card').forEach(card => {
                card.addEventListener('click', () => selectBreed(card.dataset.breed));
            });

            // 滑块事件
            brightnessSlider.addEventListener('input', updateSliderValues);
            contrastSlider.addEventListener('input', updateSliderValues);
            resolutionSlider.addEventListener('input', updateSliderValues);
            frameRateSlider.addEventListener('input', updateFrameRateValue);
            colorIntensitySlider.addEventListener('input', updateSliderValues);

            // 处理按钮
            processBtn.addEventListener('click', processVideo);

            // 下载按钮
            downloadBtn.addEventListener('click', downloadVideo);
            
            // 预览按钮
            previewBtn.addEventListener('click', generatePreview);
        }

        // 拖拽处理
        function handleDragOver(e) {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        // 文件选择处理
        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        // 处理选中的文件
        function handleFile(file) {
            if (!file.type.startsWith('video/')) {
                showStatus('请选择有效的视频文件', 'error');
                return;
            }

            originalVideo = file;
            const url = URL.createObjectURL(file);
            originalVideoEl.src = url;
            
            uploadZone.innerHTML = `
                <div class="upload-icon">✅</div>
                <p><strong>已选择文件：</strong>${file.name}</p>
                <p>文件大小：${(file.size / 1024 / 1024).toFixed(2)} MB</p>
            `;

            showStatus('视频文件加载成功', 'success');
            checkReadiness();
        }

        // 选择动物类型
        function selectSpecies(species) {
            selectedSpecies = species;
            speciesCards.forEach(card => card.classList.remove('selected'));
            document.querySelector(`[data-species="${species}"]`).classList.add('selected');

            breedSelector.style.display = 'block';
            dogBreeds.style.display = species === 'dog' ? 'grid' : 'none';
            catBreeds.style.display = species === 'cat' ? 'grid' : 'none';

            // 重置品种选择
            selectedBreed = null;
            document.querySelectorAll('.breed-card').forEach(card => card.classList.remove('selected'));
            
            checkReadiness();
        }

        // 选择品种
        function selectBreed(breed) {
            selectedBreed = breed;
            document.querySelectorAll('.breed-card').forEach(card => card.classList.remove('selected'));
            document.querySelector(`[data-breed="${breed}"]`).classList.add('selected');

            // 应用品种特定参数
            applyBreedParams(breed);
            
            controlsSection.style.display = 'block';
            checkReadiness();
        }

        // 应用品种参数
        function applyBreedParams(breed) {
            const config = selectedSpecies === 'dog' ? VISION_CONFIG.DOG : VISION_CONFIG.CAT;
            const breedConfig = config.BREEDS[breed] || config.BASE;

            brightnessSlider.value = breedConfig.BRIGHTNESS;
            contrastSlider.value = breedConfig.CONTRAST;
            resolutionSlider.value = breedConfig.RESOLUTION_FACTOR;

            updateSliderValues();
            
            showStatus(`已应用 ${breed} 的视觉参数`, 'info');
        }

        // 更新滑块显示值
        function updateSliderValues() {
            brightnessValue.textContent = brightnessSlider.value;
            contrastValue.textContent = contrastSlider.value;
            resolutionValue.textContent = resolutionSlider.value;
            if (colorIntensityValue) {
                colorIntensityValue.textContent = colorIntensitySlider.value;
            }
        }
        
        // 更新帧率显示值
        function updateFrameRateValue() {
            frameRateValue.textContent = frameRateSlider.value + ' fps';
            targetFrameRate = parseInt(frameRateSlider.value);
        }
        
        // 实时更新性能监控显示
        function updatePerformanceMonitor() {
            if (isProcessing && performanceMonitor.style.display !== 'none') {
                document.getElementById('currentFps').textContent = adaptiveFrameRate.toFixed(1);
                document.getElementById('avgFrameTime').textContent = processingStats.averageFrameTime.toFixed(2);
                document.getElementById('processedFrames').textContent = processingStats.frameCount;
                
                const dropRate = processingStats.frameCount > 0 ? 
                    ((processingStats.droppedFrames / processingStats.frameCount) * 100).toFixed(1) : '0.0';
                document.getElementById('dropRate').textContent = dropRate;
            }
        }
        
        // 开始性能监控
        function startPerformanceMonitoring() {
            performanceMonitor.style.display = 'block';
            const monitorInterval = setInterval(() => {
                if (!isProcessing) {
                    clearInterval(monitorInterval);
                    return;
                }
                updatePerformanceMonitor();
            }, 500); // 每500ms更新一次显示
        }

        // 检查是否准备就绪
        function checkReadiness() {
            const ready = originalVideo && selectedSpecies && selectedBreed;
            processBtn.disabled = !ready || isProcessing;
            
            if (ready) {
                processBtn.textContent = '🚀 开始转换';
                performanceSection.style.display = 'block'; // 显示性能设置
                previewSection.style.display = 'block'; // 显示预览区域
            } else {
                processBtn.textContent = '请完成上述步骤';
                performanceSection.style.display = 'none'; // 隐藏性能设置
                previewSection.style.display = 'none'; // 隐藏预览区域
            }
        }
        
        // 生成预览
        function generatePreview() {
            if (!originalVideo) {
                showStatus('请先选择视频文件', 'error');
                return;
            }
            
            try {
                // 创建临时视频元素
                const tempVideo = document.createElement('video');
                tempVideo.src = URL.createObjectURL(originalVideo);
                tempVideo.crossOrigin = 'anonymous';
                
                tempVideo.addEventListener('loadeddata', () => {
                    // 设置视频到中间帧进行预览
                    tempVideo.currentTime = tempVideo.duration / 2;
                });
                
                tempVideo.addEventListener('seeked', () => {
                    try {
                        // 获取canvas上下文
                        const originalCtx = originalCanvas.getContext('2d');
                        const previewCtx = previewCanvas.getContext('2d');
                        
                        // 绘制原始帧
                        originalCtx.drawImage(tempVideo, 0, 0, 320, 240);
                        
                        // 绘制处理后的帧
                        previewCtx.drawImage(tempVideo, 0, 0, 320, 240);
                        applyVisionEffects(previewCtx, 320, 240);
                        
                        // 显示预览
                        livePreview.style.display = 'grid';
                        
                        // 清理临时视频元素
                        URL.revokeObjectURL(tempVideo.src);
                        
                        showStatus('预览生成成功！可以调整参数后重新预览', 'success');
                        
                    } catch (error) {
                        console.error('预览生成错误:', error);
                        showStatus('预览生成失败: ' + error.message, 'error');
                    }
                });
                
                tempVideo.load();
                
            } catch (error) {
                console.error('预览初始化错误:', error);
                showStatus('预览功能初始化失败: ' + error.message, 'error');
            }
        }

        // 显示状态消息
        function showStatus(message, type) {
            statusMessage.textContent = message;
            statusMessage.className = `status-message status-${type}`;
            statusMessage.style.display = 'block';
            
            setTimeout(() => {
                statusMessage.style.display = 'none';
            }, 5000);
        }

        // 处理视频
        async function processVideo() {
            if (isProcessing || !originalVideo) return;
            
            isProcessing = true;
            processBtn.disabled = true;
            processBtn.textContent = '处理中...';
            progressContainer.style.display = 'block';
            
            try {
                showStatus('开始处理视频...', 'info');
                updateProgress(0, '初始化处理...');
                
                // 获取用户性能设置
                targetFrameRate = parseInt(frameRateSlider.value);
                const useHighQuality = highQualityCheck.checked;
                const useAdaptiveFrameRate = adaptiveFrameRateCheck.checked;
                
                // 启动性能监控
                startPerformanceMonitoring();
                
                // 创建视频元素用于处理
                const video = document.createElement('video');
                video.src = URL.createObjectURL(originalVideo);
                video.crossOrigin = 'anonymous';
                
                await new Promise((resolve) => {
                    video.addEventListener('loadedmetadata', resolve);
                    video.load();
                });
                
                updateProgress(10, '视频加载完成...');
                
                // 设置canvas
                const canvas = processCanvas;
                const ctx = canvas.getContext('2d');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                
                updateProgress(20, '准备视频处理...');
                
                // 重置性能统计
                processingStats = {
                    frameCount: 0,
                    totalProcessingTime: 0,
                    averageFrameTime: 0,
                    droppedFrames: 0
                };
                adaptiveFrameRate = 30;
                
                // 创建MediaRecorder进行录制，使用自适应帧率
                const stream = canvas.captureStream(adaptiveFrameRate);
                
                // 优化编码器设置
                let mimeType = 'video/webm;codecs=vp8'; // 使用VP8替代VP9，兼容性更好
                if (!MediaRecorder.isTypeSupported(mimeType)) {
                    mimeType = 'video/webm'; // 回退选项
                }
                
                const recorder = new MediaRecorder(stream, {
                    mimeType: mimeType,
                    videoBitsPerSecond: 2000000 // 2Mbps，平衡质量和文件大小
                });
                
                const chunks = [];
                recorder.ondataavailable = (e) => {
                    if (e.data.size > 0) {
                        chunks.push(e.data);
                    }
                };
                
                recorder.onstop = () => {
                    const blob = new Blob(chunks, { type: mimeType });
                    const url = URL.createObjectURL(blob);
                    processedVideoEl.src = url;
                    videoPreview.style.display = 'grid';
                    downloadSection.style.display = 'block';
                    
                    // 存储处理后的视频用于下载
                    window.processedVideoBlob = blob;
                    
                    // 显示性能统计
                    const avgTime = processingStats.averageFrameTime.toFixed(2);
                    const dropRate = ((processingStats.droppedFrames / processingStats.frameCount) * 100).toFixed(1);
                    
                    updateProgress(100, '处理完成！');
                    showStatus(`视频处理完成！平均帧时间: ${avgTime}ms，掉帧率: ${dropRate}%`, 'success');
                    
                    isProcessing = false;
                    processBtn.disabled = false;
                    processBtn.textContent = '🚀 开始转换';
                    
                    setTimeout(() => {
                        progressContainer.style.display = 'none';
                    }, 2000);
                };
                
                recorder.start(100); // 每100ms收集一次数据块
                video.play();
                
                // 优化后的帧处理逻辑
                const originalFrameRate = 30;
                const frameCount = Math.floor(video.duration * originalFrameRate);
                let currentFrame = 0;
                let lastProcessTime = 0;
                let skippedFrames = 0;
                
                const processFrame = () => {
                    if (video.ended || video.paused) {
                        recorder.stop();
                        console.log(`视频处理完成。总帧数: ${currentFrame}, 跳过帧数: ${skippedFrames}`);
                        return;
                    }
                    
                    const currentTime = performance.now();
                    const deltaTime = currentTime - lastProcessTime;
                    const targetFrameInterval = 1000 / adaptiveFrameRate;
                    
                    // 帧率控制：只有达到目标间隔才处理帧
                    if (deltaTime >= targetFrameInterval - 2) { // 2ms容错
                        lastProcessTime = currentTime;
                        
                        try {
                            // 绘制当前帧到canvas
                            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                            
                            // 应用宠物视觉效果
                            applyVisionEffects(ctx, canvas.width, canvas.height);
                            
                            currentFrame++;
                            
                            // 更新进度，考虑实际处理的帧数
                            const actualProgress = (currentFrame / frameCount) * 70;
                            const progress = Math.min(90, 20 + actualProgress);
                            const statusText = `处理帧 ${currentFrame}/${frameCount} (帧率: ${adaptiveFrameRate.toFixed(1)}fps)`;
                            updateProgress(progress, statusText);
                            
                        } catch (error) {
                            console.error('处理帧错误:', error);
                            // 错误时跳过当前帧
                            skippedFrames++;
                        }
                    } else {
                        // 跳过这一帧，等待下一帧
                        skippedFrames++;
                    }
                    
                    // 继续处理下一帧
                    requestAnimationFrame(processFrame);
                };
                
                updateProgress(30, '开始处理视频帧...');
                lastProcessTime = performance.now();
                processFrame();
                
            } catch (error) {
                console.error('视频处理错误:', error);
                showStatus('视频处理失败: ' + error.message, 'error');
                
                isProcessing = false;
                processBtn.disabled = false;
                processBtn.textContent = '🚀 开始转换';
                progressContainer.style.display = 'none';
            }
        }

        // 优化后的视觉效果处理函数
        function applyVisionEffects(ctx, width, height) {
            const startTime = performance.now();
            
            if (!colorVisionCheck.checked && !nightVisionCheck.checked && !motionVisionCheck.checked) {
                return;
            }
            
            // 复用缓冲区，避免重复分配内存
            if (!imageDataBuffer || imageDataBuffer.width !== width || imageDataBuffer.height !== height) {
                imageDataBuffer = ctx.createImageData(width, height);
                processedImageData = ctx.createImageData(width, height);
            }
            
            // 获取图像数据
            const sourceData = ctx.getImageData(0, 0, width, height);
            const sourcePixels = sourceData.data;
            const targetPixels = imageDataBuffer.data;
            
            // 预计算参数，避免在循环中重复计算
            const brightness = parseFloat(brightnessSlider.value);
            const contrast = parseFloat(contrastSlider.value);
            const resolution = parseFloat(resolutionSlider.value);
            const colorIntensity = parseFloat(colorIntensitySlider.value);
            
            const applyColorVision = colorVisionCheck.checked;
            const applyNightVision = nightVisionCheck.checked;
            const applyMotionVision = motionVisionCheck.checked;
            const applyPeripheralDim = peripheralDimCheck.checked;
            const isDogVision = selectedSpecies === 'dog';
            const blurFactor = resolution < 1.0 ? (1.0 - resolution) : 0;
            
            // 计算中心点用于周边暗化效果
            const centerX = width / 2;
            const centerY = height / 2;
            const maxDistance = Math.sqrt(centerX * centerX + centerY * centerY);
            
            // 性能优化：根据分辨率和质量设置采样步长
            const useHighQuality = highQualityCheck ? highQualityCheck.checked : true;
            let sampleStep;
            if (useHighQuality) {
                sampleStep = resolution < 0.3 ? 3 : (resolution < 0.7 ? 2 : 1);
            } else {
                sampleStep = resolution < 0.3 ? 6 : (resolution < 0.7 ? 4 : 2);
            }
            
            // 批量处理像素，提高缓存效率
            for (let y = 0; y < height; y += sampleStep) {
                for (let x = 0; x < width; x += sampleStep) {
                    const i = (y * width + x) * 4;
                    
                    let r = sourcePixels[i];
                    let g = sourcePixels[i + 1];
                    let b = sourcePixels[i + 2];
                    const a = sourcePixels[i + 3];
                    
                    // 保存原始颜色用于混合
                    const originalR = r, originalG = g, originalB = b;
                    
                    // 二色视觉转换（基于真实动物色觉）
                    if (applyColorVision) {
                        if (isDogVision) {
                            // 犬科二色视觉：红绿色盲，主要感知蓝-黄色轴
                            // 模拟L型视锥细胞缺失，保留S型(蓝)和M型(绿-黄)
                            
                            // 将红-绿信息转换为蓝-黄感知
                            const luminance = r * 0.299 + g * 0.587 + b * 0.114; // 亮度保持
                            
                            // 计算蓝-黄对比度
                            const blueYellowContrast = (b - (r + g) * 0.5) * 0.8;
                            
                            // 重新分配颜色通道
                            // 增强蓝色感知，减弱红绿区分
                            const newR = luminance + blueYellowContrast * 0.3; // 轻微黄色倾向
                            const newG = luminance + blueYellowContrast * 0.5; // 中等黄色倾向  
                            const newB = Math.min(255, b + blueYellowContrast * 0.8); // 增强蓝色
                            
                            // 应用强度控制的混合
                            r = originalR * (1 - colorIntensity) + newR * colorIntensity;
                            g = originalG * (1 - colorIntensity) + newG * colorIntensity;
                            b = originalB * (1 - colorIntensity) + newB * colorIntensity;
                            
                        } else {
                            // 猫科二色视觉：类似人类红绿色盲，但对蓝紫色更敏感
                            const luminance = r * 0.299 + g * 0.587 + b * 0.114;
                            
                            // 增强蓝紫色感知，减少红绿对比
                            const blueIntensity = b / 255;
                            const redGreenAvg = (r + g) * 0.5;
                            
                            // 保持蓝色敏感性，将红绿混合
                            const newR = redGreenAvg * 0.8 + luminance * 0.2 + blueIntensity * 15;
                            const newG = redGreenAvg * 0.9 + luminance * 0.1;
                            const newB = Math.min(255, b * 1.3 + blueIntensity * 30); // 增强蓝色
                            
                            // 应用强度控制的混合
                            r = originalR * (1 - colorIntensity) + newR * colorIntensity;
                            g = originalG * (1 - colorIntensity) + newG * colorIntensity;
                            b = originalB * (1 - colorIntensity) + newB * colorIntensity;
                        }
                    }
                    
                    // 运动感知增强（边缘检测和对比度增强）
                    if (applyMotionVision) {
                        // 简化的边缘检测：检查与周围像素的对比度
                        if (x > 0 && x < width - 1 && y > 0 && y < height - 1) {
                            const leftIdx = (y * width + (x - 1)) * 4;
                            const rightIdx = (y * width + (x + 1)) * 4;
                            const topIdx = ((y - 1) * width + x) * 4;
                            const bottomIdx = ((y + 1) * width + x) * 4;
                            
                            // 计算梯度强度
                            const gradientX = Math.abs(sourcePixels[rightIdx] - sourcePixels[leftIdx]);
                            const gradientY = Math.abs(sourcePixels[bottomIdx] - sourcePixels[topIdx]);
                            const edgeStrength = (gradientX + gradientY) / 2;
                            
                            // 增强边缘对比度（模拟动物对运动的敏感性）
                            if (edgeStrength > 20) {
                                const enhancement = Math.min(1.3, 1 + edgeStrength / 200);
                                r *= enhancement;
                                g *= enhancement;
                                b *= enhancement;
                            }
                        }
                    }
                    
                    // 周边视野暗化（模拟动物的中心视觉优势）
                    if (applyPeripheralDim) {
                        const distanceFromCenter = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
                        const normalizedDistance = distanceFromCenter / maxDistance;
                        
                        // 从中心到边缘逐渐暗化
                        if (normalizedDistance > 0.3) {
                            const dimFactor = 1 - (normalizedDistance - 0.3) * 0.6; // 0.7倍到1倍
                            r *= dimFactor;
                            g *= dimFactor;
                            b *= dimFactor;
                        }
                    }
                    
                    // 夜视增强（优化后）
                    if (applyNightVision) {
                        r *= brightness;
                        g *= brightness;
                        b *= brightness;
                        
                        // 对比度调整
                        r = (r - 128) * contrast + 128;
                        g = (g - 128) * contrast + 128;
                        b = (b - 128) * contrast + 128;
                        
                        // 添加绿色色调
                        g *= 1.1;
                    }
                    
                    // 视力模糊效果（优化后）
                    if (blurFactor > 0) {
                        const grayValue = (r + g + b) * 0.333; // 使用乘法替代除法
                        r = r * (1 - blurFactor) + grayValue * blurFactor;
                        g = g * (1 - blurFactor) + grayValue * blurFactor;
                        b = b * (1 - blurFactor) + grayValue * blurFactor;
                    }
                    
                    // 快速截取到有效范围（避免Math.max/Math.min）
                    r = r < 0 ? 0 : (r > 255 ? 255 : r);
                    g = g < 0 ? 0 : (g > 255 ? 255 : g);
                    b = b < 0 ? 0 : (b > 255 ? 255 : b);
                    
                    // 如果使用采样，需要填充相邻像素
                    for (let dy = 0; dy < sampleStep && y + dy < height; dy++) {
                        for (let dx = 0; dx < sampleStep && x + dx < width; dx++) {
                            const idx = ((y + dy) * width + (x + dx)) * 4;
                            targetPixels[idx] = r;
                            targetPixels[idx + 1] = g;
                            targetPixels[idx + 2] = b;
                            targetPixels[idx + 3] = a;
                        }
                    }
                }
            }
            
            // 应用处理后的图像数据
            ctx.putImageData(imageDataBuffer, 0, 0);
            
            // 更新性能统计
            const processingTime = performance.now() - startTime;
            updateProcessingStats(processingTime);
        }
        
        // 性能统计更新函数
        function updateProcessingStats(frameTime) {
            processingStats.frameCount++;
            processingStats.totalProcessingTime += frameTime;
            processingStats.averageFrameTime = processingStats.totalProcessingTime / processingStats.frameCount;
            
            // 如果处理时间超过目标帧时间，记录掉帧
            const targetFrameTime = 1000 / adaptiveFrameRate;
            if (frameTime > targetFrameTime * 1.5) {
                processingStats.droppedFrames++;
            }
            
            // 动态调整帧率
            adjustAdaptiveFrameRate(frameTime);
        }
        
        // 自适应帧率调整
        function adjustAdaptiveFrameRate(frameTime) {
            // 检查是否启用自适应帧率
            const useAdaptiveFrameRate = adaptiveFrameRateCheck ? adaptiveFrameRateCheck.checked : true;
            if (!useAdaptiveFrameRate) {
                adaptiveFrameRate = targetFrameRate;
                return;
            }
            
            const targetFrameTime = 1000 / targetFrameRate;
            
            if (frameTime > targetFrameTime * 1.8) {
                // 处理时间过长，降低帧率
                adaptiveFrameRate = Math.max(15, adaptiveFrameRate * 0.9);
            } else if (frameTime < targetFrameTime * 0.6) {
                // 处理时间充足，可以提高帧率
                adaptiveFrameRate = Math.min(targetFrameRate, adaptiveFrameRate * 1.05);
            }
        }

        // 更新进度
        function updateProgress(percent, text) {
            progressFill.style.width = `${percent}%`;
            progressText.textContent = text;
        }

        // 下载视频
        function downloadVideo() {
            if (!window.processedVideoBlob) {
                showStatus('没有可下载的视频', 'error');
                return;
            }
            
            const url = URL.createObjectURL(window.processedVideoBlob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${selectedBreed}_视角_${Date.now()}.webm`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showStatus('视频下载开始', 'success');
        }
    </script>
</body>
</html>
