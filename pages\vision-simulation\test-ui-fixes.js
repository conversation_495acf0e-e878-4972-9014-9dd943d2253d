/**
 * UI功能修复测试脚本
 * 测试相机选择器和保存图片功能
 */

console.log('🔧 开始测试UI功能修复...');

// 模拟微信小程序环境
global.wx = {
  createSelectorQuery: () => ({
    select: (selector) => ({
      fields: (options) => ({
        exec: (callback) => {
          console.log('📋 模拟Canvas节点查询:', selector);
          // 模拟Canvas节点
          const mockCanvas = {
            getContext: (type) => {
              if (type === 'webgl') {
                console.log('✅ 模拟WebGL上下文创建成功');
                return {
                  createShader: () => ({}),
                  shaderSource: () => {},
                  compileShader: () => {},
                  getShaderParameter: () => true,
                  createProgram: () => ({}),
                  attachShader: () => {},
                  linkProgram: () => {},
                  getProgramParameter: () => true,
                  createBuffer: () => ({}),
                  bindBuffer: () => {},
                  bufferData: () => {},
                  createTexture: () => ({}),
                  bindTexture: () => {},
                  texParameteri: () => {},
                  texImage2D: () => {},
                  viewport: () => {},
                  useProgram: () => {},
                  getAttribLocation: () => 0,
                  getUniformLocation: () => ({}),
                  enableVertexAttribArray: () => {},
                  vertexAttribPointer: () => {},
                  uniform1f: () => {},
                  uniform1i: () => {},
                  uniform2f: () => {},
                  activeTexture: () => {},
                  drawArrays: () => {},
                  VERTEX_SHADER: 35633,
                  FRAGMENT_SHADER: 35632,
                  COMPILE_STATUS: 35713,
                  LINK_STATUS: 35714,
                  ARRAY_BUFFER: 34962,
                  STATIC_DRAW: 35044,
                  TEXTURE_2D: 3553,
                  RGBA: 6408,
                  UNSIGNED_BYTE: 5121,
                  CLAMP_TO_EDGE: 33071,
                  LINEAR: 9729,
                  TEXTURE0: 33984,
                  TEXTURE1: 33985,
                  TEXTURE_WRAP_S: 10242,
                  TEXTURE_WRAP_T: 10243,
                  TEXTURE_MIN_FILTER: 10241,
                  TEXTURE_MAG_FILTER: 10240,
                  TRIANGLES: 4,
                  FLOAT: 5126
                };
              }
              return null;
            },
            width: 1280,
            height: 720
          };
          
          setTimeout(() => {
            callback([{
              node: mockCanvas,
              width: 1280,
              height: 720
            }]);
          }, 50);
        }
      })
    })
  }),
  showLoading: (options) => console.log('⏳ 显示加载:', options.title),
  hideLoading: () => console.log('✅ 隐藏加载'),
  showToast: (options) => console.log('📱 提示:', options.title),
  canvasToTempFilePath: (options) => {
    console.log('📷 模拟保存画布到临时文件');
    setTimeout(() => {
      if (options.success) {
        options.success({
          tempFilePath: '/tmp/mock_image.png'
        });
      }
    }, 100);
  },
  saveImageToPhotosAlbum: (options) => {
    console.log('💾 模拟保存图片到相册:', options.filePath);
    setTimeout(() => {
      if (options.success) {
        options.success();
      }
    }, 100);
  },
  showModal: (options) => {
    console.log('📋 模拟显示模态框:', options.title, '-', options.content);
    setTimeout(() => {
      if (options.success) {
        options.success({ confirm: true });
      }
    }, 50);
  }
};

// 模拟页面实例
global.page = {
  data: {
    cameraOptions: [
      { 
        type: 'back', 
        name: '后置相机', 
        icon: '📷', 
        desc: '手机后置摄像头',
        available: true 
      },
      { 
        type: 'front', 
        name: '前置相机', 
        icon: '🤳', 
        desc: '手机前置摄像头',
        available: true 
      },
      { 
        type: 'cam', 
        name: '硬件相机', 
        icon: '📡', 
        desc: 'CAM硬件摄像设备',
        available: true 
      }
    ],
    currentCameraType: 'back',
    showCameraSelector: false,
    currentView: 'dog',
    breedName: '金毛犬',
    features: {
      isCat: false
    }
  },
  setData: (data) => {
    console.log('📊 页面状态更新:', Object.keys(data).join(', '));
    Object.assign(global.page.data, data);
  }
};

// 初始化全局变量
global.webglContext = null;

// 测试函数
async function testUIFixes() {
  try {
    console.log('\n=== 测试1: 相机选择器数据 ===');
    
    // 检查相机选项数据
    const cameraOptions = global.page.data.cameraOptions;
    if (cameraOptions && cameraOptions.length === 3) {
      console.log('✅ 相机选项数据正确');
      console.log('📋 相机选项:');
      cameraOptions.forEach((option, index) => {
        console.log(`  ${index + 1}. ${option.icon} ${option.name} - ${option.desc}`);
      });
    } else {
      throw new Error('相机选项数据不正确');
    }
    
    console.log('\n=== 测试2: 相机选择器切换 ===');
    
    // 测试切换相机选择器
    const uiEventHandler = require('./utils/ui-event-handler');
    
    console.log('当前显示状态:', global.page.data.showCameraSelector);
    uiEventHandler.toggleCameraSelector(global.page);
    console.log('切换后状态:', global.page.data.showCameraSelector);
    
    if (global.page.data.showCameraSelector) {
      console.log('✅ 相机选择器切换功能正常');
    } else {
      throw new Error('相机选择器切换失败');
    }
    
    console.log('\n=== 测试3: WebGL上下文初始化 ===');
    
    // 初始化WebGL上下文
    const webglRenderer = require('./utils/webgl-renderer');
    const webglContext = await webglRenderer.initWebGL('processCanvas');
    
    if (webglContext && webglContext.canvas) {
      console.log('✅ WebGL上下文初始化成功，包含canvas引用');
      global.webglContext = webglContext;
    } else {
      throw new Error('WebGL上下文初始化失败或缺少canvas引用');
    }
    
    console.log('\n=== 测试4: 保存图片功能 ===');
    
    // 测试保存图片功能
    const imageSaveManager = require('./utils/image-save-manager');
    
    console.log('开始测试保存图片功能...');
    imageSaveManager.savePetVisionImage(global.page);
    
    // 等待异步操作完成
    await new Promise(resolve => setTimeout(resolve, 500));
    
    console.log('✅ 保存图片功能测试完成');
    
    console.log('\n🎉 所有UI功能测试通过！');
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    console.error('详细错误:', error);
  }
}

// 运行测试
testUIFixes();
