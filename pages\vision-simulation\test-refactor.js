/**
 * 重构测试脚本
 * 验证重构后的模块是否正常工作
 */

// 模拟微信小程序环境
global.wx = {
  getStorageSync: () => null,
  setStorageSync: () => {},
  showToast: () => {},
  showModal: () => {},
  vibrateShort: () => {},
  onAppShow: () => {},
  onAppHide: () => {},
  offAppShow: () => {},
  offAppHide: () => {},
  navigateBack: () => {},
  getSystemInfo: () => {}
};

global.getApp = () => ({
  globalData: {
    selectedBreed: { name: '金毛', details: {} },
    breedType: 'dog',
    currentTheme: 'theme1',
    themeConfig: {
      gradient: 'linear-gradient(145deg, #3D6B96, #5284B4)',
      darkBg: '#3D6B96',
      cardBg: 'rgba(70, 115, 160, 0.85)'
    }
  },
  themeChangeCallbacks: []
});

// 测试模块导入
function testModuleImports() {
  console.log('测试模块导入...');
  
  try {
    const lifecycleManager = require('./utils/lifecycle-manager');
    console.log('✓ lifecycle-manager 导入成功');
    
    const cameraController = require('./utils/camera-controller');
    console.log('✓ camera-controller 导入成功');
    
    const visionParamsManager = require('./utils/vision-params-manager');
    console.log('✓ vision-params-manager 导入成功');
    
    const uiEventHandler = require('./utils/ui-event-handler');
    console.log('✓ ui-event-handler 导入成功');
    
    const imageSaveManager = require('./utils/image-save-manager');
    console.log('✓ image-save-manager 导入成功');
    
    return true;
  } catch (error) {
    console.error('✗ 模块导入失败:', error);
    return false;
  }
}

// 测试页面实例化
function testPageInstantiation() {
  console.log('测试页面实例化...');
  
  try {
    // 模拟Page函数
    global.Page = function(config) {
      console.log('✓ Page配置对象创建成功');
      console.log('✓ 包含方法数量:', Object.keys(config).length);
      
      // 检查关键方法是否存在
      const requiredMethods = [
        'onLoad', 'onReady', 'onShow', 'onHide', 'onUnload',
        'processFrameWebGL', 'updateTheme', 'initCamera',
        'toggleView', 'savePetVisionImage'
      ];
      
      const missingMethods = requiredMethods.filter(method => !config[method]);
      
      if (missingMethods.length === 0) {
        console.log('✓ 所有关键方法都存在');
        return true;
      } else {
        console.error('✗ 缺少方法:', missingMethods);
        return false;
      }
    };
    
    // 加载重构后的页面
    require('./vision-simulation');
    
    return true;
  } catch (error) {
    console.error('✗ 页面实例化失败:', error);
    return false;
  }
}

// 测试模块方法调用
function testModuleMethods() {
  console.log('测试模块方法调用...');
  
  try {
    const lifecycleManager = require('./utils/lifecycle-manager');
    const mockPage = {
      setData: () => {},
      data: {
        breedName: '金毛',
        features: { isCat: false }
      }
    };
    
    // 测试生命周期管理器
    if (typeof lifecycleManager.onLoad === 'function') {
      console.log('✓ lifecycleManager.onLoad 方法存在');
    }
    
    if (typeof lifecycleManager.startFPSCounter === 'function') {
      console.log('✓ lifecycleManager.startFPSCounter 方法存在');
    }
    
    // 测试视觉参数管理器
    const visionParamsManager = require('./utils/vision-params-manager');
    if (typeof visionParamsManager.setBreedVisualAcuity === 'function') {
      console.log('✓ visionParamsManager.setBreedVisualAcuity 方法存在');
    }
    
    // 测试UI事件处理器
    const uiEventHandler = require('./utils/ui-event-handler');
    if (typeof uiEventHandler.toggleView === 'function') {
      console.log('✓ uiEventHandler.toggleView 方法存在');
    }
    
    return true;
  } catch (error) {
    console.error('✗ 模块方法测试失败:', error);
    return false;
  }
}

// 运行所有测试
function runTests() {
  console.log('开始重构验证测试...\n');
  
  const tests = [
    { name: '模块导入测试', fn: testModuleImports },
    { name: '页面实例化测试', fn: testPageInstantiation },
    { name: '模块方法测试', fn: testModuleMethods }
  ];
  
  let passedTests = 0;
  
  tests.forEach(test => {
    console.log(`\n--- ${test.name} ---`);
    if (test.fn()) {
      passedTests++;
      console.log(`✓ ${test.name} 通过`);
    } else {
      console.log(`✗ ${test.name} 失败`);
    }
  });
  
  console.log(`\n=== 测试结果 ===`);
  console.log(`通过: ${passedTests}/${tests.length}`);
  console.log(`成功率: ${Math.round(passedTests / tests.length * 100)}%`);
  
  if (passedTests === tests.length) {
    console.log('🎉 所有测试通过！重构成功！');
  } else {
    console.log('⚠️  部分测试失败，需要检查重构代码');
  }
  
  return passedTests === tests.length;
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testModuleImports,
  testPageInstantiation,
  testModuleMethods
};
