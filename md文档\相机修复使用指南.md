# 相机修复使用指南

## 🚨 问题现象
- 相机启动失败，显示"相机启动失败，请检查相机权限或重启应用"
- 点击切换视角按钮能看到前置相机画面，但初始化时失败
- 相机加载中状态一直不消失

## 🔧 修复方案

### 方案一：智能修复按钮 ⭐ 推荐
1. **点击"智能修复"按钮**
   - 在相机错误界面，点击绿色的"智能修复"按钮
   - 系统会自动诊断并修复问题
   - 修复成功后相机会自动重新初始化

### 方案二：控制台快速修复
如果智能修复按钮无效，可以使用控制台命令：

1. **打开开发者工具控制台**
2. **运行以下命令之一：**

```javascript
// 完整诊断和修复
fullCameraFix()

// 简单修复
fixCameraInit()

// 强制重启相机
forceRestartCamera()

// 检查相机状态
checkCameraStatus()

// 切换到后置相机并修复
switchCameraAndFix('back')

// 切换到前置相机并修复
switchCameraAndFix('front')
```

### 方案三：手动操作修复
1. **切换相机视角**
   - 点击右上角的相机切换按钮
   - 尝试切换到前置相机，再切换回后置相机

2. **重新进入页面**
   - 返回上一页，再重新进入视觉模拟页面

3. **重启小程序**
   - 完全退出小程序，重新打开

## 🔍 问题原因分析

### 技术原因
相机初始化失败的主要原因是：
1. **方法调用错误** - 代码试图调用不存在的相机管理方法
2. **资源清理不完整** - 旧的相机资源没有正确释放
3. **初始化时序问题** - 相机初始化的时机不正确

### 已修复的问题
✅ **修复了方法调用错误**
- 更新了 `camera-controller.js` 使用正确的相机管理方法
- 统一了相机初始化流程

✅ **改进了资源管理**
- 正确清理相机上下文和WebGL资源
- 避免资源冲突

✅ **优化了错误处理**
- 添加了智能修复功能
- 提供了多种修复方案

## 📱 使用步骤

### 当遇到相机问题时：

1. **首先尝试智能修复**
   ```
   点击 "智能修复" 按钮 → 等待自动修复 → 相机重新初始化
   ```

2. **如果智能修复无效**
   ```
   打开控制台 → 运行 fullCameraFix() → 查看修复结果
   ```

3. **如果仍然无效**
   ```
   尝试切换相机视角 → 或重新进入页面 → 或重启小程序
   ```

## 🛠️ 开发者调试

### 控制台命令详解

#### `fullCameraFix()`
- **功能**: 完整的相机诊断和修复
- **输出**: 详细的状态检查和修复过程
- **适用**: 首次尝试修复时使用

#### `checkCameraStatus()`
- **功能**: 检查当前相机状态
- **输出**: 相机上下文、加载状态、错误信息等
- **适用**: 了解当前相机状态

#### `fixCameraInit()`
- **功能**: 快速修复相机初始化
- **输出**: 修复过程日志
- **适用**: 已知问题的快速修复

#### `forceRestartCamera()`
- **功能**: 强制重启相机
- **输出**: 重启过程日志
- **适用**: 相机完全卡死时使用

### 日志分析
修复过程中会输出详细日志：
- ✅ 表示操作成功
- ❌ 表示操作失败
- ⏳ 表示操作进行中
- 🔧 表示修复操作
- 📋 表示检查步骤

## 🎯 预防措施

### 最佳实践
1. **避免频繁切换相机模式**
2. **等待相机完全初始化后再进行操作**
3. **定期清理小程序缓存**
4. **保持微信版本更新**

### 环境要求
- **微信版本**: 7.0+
- **系统版本**: iOS 10.0+ / Android 6.0+
- **设备要求**: 支持相机和WebGL

## 📞 技术支持

### 如果问题仍未解决
1. **记录错误日志**
   - 打开控制台查看详细错误信息
   - 截图保存错误界面

2. **提供设备信息**
   - 设备型号和系统版本
   - 微信版本
   - 问题复现步骤

3. **联系开发者**
   - 通过小程序内反馈功能
   - 或联系技术支持

## 🔄 更新日志

### v1.0 (当前版本)
- ✅ 修复了相机初始化方法调用错误
- ✅ 添加了智能修复功能
- ✅ 提供了多种修复方案
- ✅ 改进了错误处理和资源管理

---

> 💡 **提示**: 大部分相机问题都可以通过"智能修复"按钮解决。如果问题持续存在，请尝试控制台命令或联系技术支持。
