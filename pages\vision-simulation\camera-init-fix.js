/**
 * 相机初始化修复脚本
 * 专门解决相机初始化失败的问题
 */

// 修复相机初始化问题的主函数
function fixCameraInit() {
  console.log('🔧 开始修复相机初始化问题...');
  
  // 检查页面实例
  if (!global.page) {
    console.error('❌ 页面实例不存在');
    return false;
  }
  
  const page = global.page;
  
  try {
    console.log('📋 步骤1: 清理现有相机资源...');
    
    // 停止现有相机
    if (page.cameraCtx) {
      try {
        const cameraManager = require('./utils/camera-manager');
        cameraManager.stopCamera(page.cameraCtx);
        console.log('✅ 现有相机已停止');
      } catch (error) {
        console.error('停止相机时出错:', error);
      }
      page.cameraCtx = null;
    }
    
    // 清理WebGL上下文
    if (global.webglContext) {
      try {
        console.log('🧹 清理WebGL资源...');
        global.webglContext = null;
        console.log('✅ WebGL资源已清理');
      } catch (error) {
        console.error('清理WebGL时出错:', error);
      }
    }
    
    console.log('📋 步骤2: 重置页面状态...');
    
    // 重置页面状态
    page.setData({
      cameraLoading: true,
      cameraError: false,
      cameraErrorMsg: '',
      showCameraSettingBtn: false,
      cameraInitializing: false
    });
    
    console.log('✅ 页面状态已重置');
    
    console.log('📋 步骤3: 重新初始化相机...');
    
    // 延迟重新初始化，给系统时间清理资源
    setTimeout(() => {
      try {
        const cameraManager = require('./utils/camera-manager');
        
        cameraManager.initCamera(page)
          .then(cameraContext => {
            if (cameraContext) {
              page.cameraCtx = cameraContext;
              console.log('✅ 相机初始化成功！');
              
              // 显示成功提示
              if (typeof wx !== 'undefined') {
                wx.showToast({
                  title: '相机修复成功',
                  icon: 'success',
                  duration: 2000
                });
              }
              
              return true;
            } else {
              throw new Error('相机上下文为空');
            }
          })
          .catch(error => {
            console.error('❌ 相机初始化仍然失败:', error);
            
            // 显示失败提示
            if (typeof wx !== 'undefined') {
              wx.showToast({
                title: '相机修复失败',
                icon: 'none',
                duration: 2000
              });
            }
            
            return false;
          });
      } catch (error) {
        console.error('❌ 重新初始化相机时出错:', error);
        return false;
      }
    }, 1000);
    
    return true;
    
  } catch (error) {
    console.error('❌ 修复过程中出错:', error);
    return false;
  }
}

// 检查相机状态
function checkCameraStatus() {
  console.log('🔍 检查相机状态...');
  
  if (!global.page) {
    console.log('❌ 页面实例不存在');
    return;
  }
  
  const page = global.page;
  
  console.log('📊 当前相机状态:');
  console.log('  - 相机上下文:', page.cameraCtx ? '✅ 存在' : '❌ 不存在');
  console.log('  - 相机加载中:', page.data.cameraLoading ? '⏳ 是' : '✅ 否');
  console.log('  - 相机错误:', page.data.cameraError ? '❌ 是' : '✅ 否');
  console.log('  - 错误消息:', page.data.cameraErrorMsg || '无');
  console.log('  - CAM模式:', page.data.camMode ? '✅ 是' : '❌ 否');
  console.log('  - 相机位置:', page.data.cameraPosition || '未设置');
  console.log('  - WebGL上下文:', global.webglContext ? '✅ 存在' : '❌ 不存在');
}

// 强制重启相机
function forceRestartCamera() {
  console.log('🔄 强制重启相机...');
  
  if (!global.page) {
    console.error('❌ 页面实例不存在');
    return;
  }
  
  const page = global.page;
  
  // 如果是CAM模式，先切换到手机相机
  if (page.data.camMode) {
    console.log('📱 从CAM模式切换到手机相机...');
    page.disableCamMode();
    
    setTimeout(() => {
      fixCameraInit();
    }, 1500);
  } else {
    fixCameraInit();
  }
}

// 切换相机位置并修复
function switchCameraAndFix(position = 'back') {
  console.log(`🔄 切换到${position === 'back' ? '后置' : '前置'}相机并修复...`);
  
  if (!global.page) {
    console.error('❌ 页面实例不存在');
    return;
  }
  
  const page = global.page;
  
  // 设置相机位置
  page.setData({
    cameraPosition: position
  });
  
  // 修复相机
  fixCameraInit();
}

// 完整的相机诊断和修复
function fullCameraFix() {
  console.log('🏥 开始完整的相机诊断和修复...');
  console.log('==========================================');
  
  // 1. 检查当前状态
  checkCameraStatus();
  
  console.log('');
  console.log('📋 开始修复流程...');
  
  // 2. 尝试修复
  const success = fixCameraInit();
  
  if (success) {
    console.log('✅ 修复流程已启动');
    
    // 3. 延迟检查修复结果
    setTimeout(() => {
      console.log('');
      console.log('📊 修复后状态检查:');
      checkCameraStatus();
    }, 3000);
  } else {
    console.log('❌ 修复流程启动失败');
  }
  
  console.log('==========================================');
}

// 导出函数供控制台使用
if (typeof window !== 'undefined') {
  window.fixCameraInit = fixCameraInit;
  window.checkCameraStatus = checkCameraStatus;
  window.forceRestartCamera = forceRestartCamera;
  window.switchCameraAndFix = switchCameraAndFix;
  window.fullCameraFix = fullCameraFix;
}

// 自动执行修复（如果检测到相机问题）
function autoFixIfNeeded() {
  if (global.page && global.page.data.cameraError) {
    console.log('🚨 检测到相机错误，自动启动修复...');
    fullCameraFix();
  }
}

// 延迟检查是否需要自动修复
setTimeout(autoFixIfNeeded, 2000);

module.exports = {
  fixCameraInit,
  checkCameraStatus,
  forceRestartCamera,
  switchCameraAndFix,
  fullCameraFix
};
