/* 核心容器样式 */
.container {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background: var(--dark-bg);
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
  transition: background 0.5s ease; /* 添加平滑过渡动画 */
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* 导航栏样式 */
.navbar {
  height: 40px; /* 恢复导航栏高度为标准高度 */
  display: flex;
  align-items: center;
  padding: 0;
  background: rgba(66, 82, 128, 0.95); /* 更亮的导航栏背景 */
  z-index: 10;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08); /* 增加边框亮度 */
  transition: background 0.5s ease, border-color 0.5s ease; /* 添加平滑过渡动画 */
}

/* 浅色主题下的导航栏样式 */
.light-theme-content .navbar {
  background: rgba(250, 250, 250, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.back-btn {
  position: absolute;
  left: 8px;
  padding: 5px;
  color: var(--primary-color);
  font-weight: 600;
  font-size: 20px;
  background: rgba(255, 138, 91, 0.15); /* 更亮的按钮背景 */
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  margin-left: 0;
}

/* 扩大触摸区域 */
.back-btn::after {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
}

.back-arrow {
  font-size: 22px;
  line-height: 1;
}

.back-btn-hover {
  transform: scale(0.92);
  background: rgba(255, 138, 91, 0.35); /* 更亮的悬停状态 */
}

.back-btn:active {
  transform: scale(0.95);
  background: rgba(255, 138, 91, 0.25); /* 更亮的激活状态 */
}

.navbar-title {
  flex: 1;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 移除不需要的导航栏动画 */
.paw-icon {
  display: none;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
  100% { transform: translateY(0px); }
}

/* 主内容区 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100vw; /* 使用视口宽度单位 */
  margin: 0; /* 移除所有外边距 */
  padding: 0; /* 移除所有内边距 */
}

/* 相机错误提示样式 */
.camera-error-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.7);
  z-index: 100;
}

.camera-error-box {
  width: 80%;
  max-width: 600rpx;
  background: rgba(40, 44, 52, 0.95);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.camera-error-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.camera-error-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff6b6b;
  text-align: center;
  margin-bottom: 16rpx;
}

.camera-error-tip {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin-bottom: 30rpx;
}

/* 错误操作按钮区 */
.camera-error-actions {
  display: flex;
  justify-content: center;
  gap: 30rpx;
  width: 100%;
  margin-top: 20rpx;
}

.camera-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 30rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.camera-action-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.2);
}

.smart-fix-btn {
  background: rgba(52, 199, 89, 0.2);
  border: 1px solid rgba(52, 199, 89, 0.4);
  box-shadow: 0 0 20rpx rgba(52, 199, 89, 0.3);
}

.smart-fix-btn:active {
  background: rgba(52, 199, 89, 0.3);
  transform: scale(0.95);
}

.retry-btn {
  background: rgba(0, 122, 255, 0.2);
  border: 1px solid rgba(0, 122, 255, 0.3);
}

.setting-btn {
  background: rgba(255, 149, 0, 0.2);
  border: 1px solid rgba(255, 149, 0, 0.3);
}

.action-btn-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
}

.action-btn-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 在横屏模式下使用水平布局 */
@media screen and (orientation: landscape) {
  .main-content {
    flex-direction: row;
    width: 100vw;
    height: 100vh;
  }
  
  .camera-container {
    width: 65vw; /* 使用视口宽度单位 */
    height: 100vh; /* 使用视口高度单位 */
    margin: 0;
    padding: 0;
  }
  
  .analysis-container {
    width: 35vw; /* 使用视口宽度单位 */
    height: 100vh; /* 使用视口高度单位 */
    border-left: 1px solid rgba(255, 138, 91, 0.15);
    border-top: none;
    margin: 0;
    padding: 0;
  }
  
  .vision-view {
    width: 65vw; /* 横屏模式下的宽度 */
    height: 100vh;
  }
}

/* 相机容器 */
.camera-container {
  flex: 3;
  display: flex;
  flex-direction: column;
  position: relative;
  background: #303D6E; /* 更亮的背景色 */
  width: 100vw; /* 使用视口宽度单位 */
  margin: 0; /* 移除所有外边距 */
  padding: 0; /* 移除所有内边距 */
  border-radius: 0; /* 移除圆角 */
  overflow: hidden;
  left: 0;
  right: 0;
  transition: all 0.3s ease;
}

/* 浅色主题下的相机容器样式 */
.light-theme-content .camera-container {
  background: #f0f0f0;
}

/* 分析容器 - 可展开版 */
.analysis-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--dark-bg);
  width: 100vw; /* 使用视口宽度单位 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: height 0.3s ease; /* 平滑过渡效果 */
  z-index: 100; /* 确保分析区域在相机区域之上，但低于控制栏 */
  margin: 0; /* 移除所有外边距 */
  padding: 0; /* 移除所有内边距 */
  border-top: 2px solid rgba(255, 255, 255, 0.1); /* 添加微妙边框 */
  box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
  height: 30vh; /* 默认高度 */
}

/* 展开状态 */
.analysis-container.expanded {
  height: 70vh; /* 展开后的高度 */
}

/* 分析区域头部拖动条 - 中心对称设计 */
.analysis-drag-handle {
  width: 100%;
  height: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10rpx 0;
  background: rgba(0, 0, 0, 0.2);
  user-select: none;
  position: relative;
  overflow: hidden;
}

/* 拖动条指示器 */
.analysis-drag-indicator {
  width: 60rpx;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 10rpx;
  margin: 0 20rpx;
  transition: all 0.3s ease;
}

/* 展开状态下的指示器样式 */
.expanded .analysis-drag-indicator {
  background: rgba(255, 255, 255, 0.7);
  width: 40rpx; /* 展开时稍微缩短 */
}

/* 文字样式 */
.drag-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 10rpx;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

/* 悬停效果 */
.analysis-drag-handle:active {
  background: rgba(0, 0, 0, 0.3);
}

/* 添加上下箭头指示器 */
.analysis-drag-handle::after {
  content: '';
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-bottom: 8rpx solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.expanded .analysis-drag-handle::after {
  border-bottom: none;
  border-top: 8rpx solid rgba(255, 255, 255, 0.5);
}

/* 增强滚动区域的可读性 */
.tab-scroll-view {
  height: calc(100% - 100rpx); /* 减去标签页和拖动条的高度 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* iOS流畅滚动 */
}

/* 展开时的内容区域样式 */
.analysis-container.expanded .tab-scroll-view {
  height: calc(100% - 100rpx); /* 展开时的滚动区域高度 */
  padding-bottom: 50rpx; /* 添加底部空间，避免内容被遮挡 */
}

/* 固定适度尺寸样式 */
.camera-container {
  flex: none;
  height: 70vh; /* 固定适度高度 */
  overflow: hidden;
  position: relative;
  z-index: 1;
}

/* 相机区域视图样式 - 优化版 */
.view-area {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  will-change: transform; /* 启用硬件加速 */
  z-index: 1;
}

.vision-view {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover; /* 使用cover而非contain，避免出现黑边 */
  transform: none !important;
  transition: none !important; /* 移除过渡效果，减少卡顿 */
  will-change: transform; /* 启用硬件加速 */
  -webkit-transform: translateZ(0); /* 强制GPU渲染 */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* 消除全屏模式特殊处理，保持一致性 */
.camera-container.fullscreen .view-area,
.camera-container.fullscreen .vision-view {
  /* 保持与普通模式相同的设置，减少重新渲染 */
}

/* 优化相机组件渲染 - 适用于所有模式 */
camera {
  transform: none !important;
  transition: none !important;
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  z-index: 1;
}

/* 优化WebGL画布渲染 - 适用于所有模式 */
canvas {
  transform: none !important;
  transition: none !important;
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  z-index: 1;
}

/* 相机容器优化 */
.camera-container {
  /* 保持现有样式 */
  transform: translateZ(0); /* 强制GPU渲染 */
  -webkit-transform: translateZ(0);
  perspective: 1000;
  -webkit-perspective: 1000;
}

/* 浅色主题下的分析容器样式 */
.light-theme-content .analysis-container {
  background: rgba(250, 250, 250, 0.95);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}

/* 标题栏样式 */
.vision-title {
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 16px;
  background: rgba(66, 82, 128, 0.9); /* 更亮的标题栏背景 */
  backdrop-filter: blur(5px);
  color: #ffffff;
}

/* 浅色主题下的标题栏样式 */
.light-theme-title {
  background: rgba(250, 250, 250, 0.9) !important;
  color: #333333 !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.vision-title {
  z-index: 5;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08); /* 增加边框亮度 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* 标题颜色区分 */
.human-title {
  color: var(--tertiary-color);
}

.dog-title {
  color: var(--primary-color);
}

/* 视图区域 */
.view-area {
  flex: 1;
  position: relative;
  overflow: hidden;
  width: 100vw; /* 使用视口宽度单位 */
  margin: 0; /* 移除所有外边距 */
  padding: 0; /* 移除所有内边距 */
}

/* 视角共享样式 */
.vision-view {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw; /* 使用视口宽度单位 */
  height: 100%;
  background: #000;
  opacity: 0;
  transition: opacity 0.3s ease;
  margin: 0; /* 移除所有外边距 */
  padding: 0; /* 移除所有内边距 */
  pointer-events: none;
}

/* 运动视觉提示样式 */
.motion-vision-tip {
  position: absolute;
  top: 120rpx; /* 从60rpx修改为120rpx，增加与顶部的距离 */
  left: 50%;
  transform: translateX(-50%) translateY(-20rpx);
  opacity: 0;
  pointer-events: none;
  z-index: 1000;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  width: 80%;
  max-width: 600rpx;
}

.motion-tip-show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
  pointer-events: auto;
  animation: fadeInDown 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

.motion-tip-content {
  background: rgba(66, 82, 128, 0.85);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.15);
  max-width: 100%; /* u786eu4fddu4e0du8d85u51fau7236u5bb9u5668 */
  overflow: hidden; /* u9632u6b62u5185u5bb9u6ea2u51fa */
}

.motion-tip-text {
  flex: 1;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.4;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  white-space: normal; /* u5141u8bb8u6587u672cu6362u884c */
  word-wrap: break-word; /* u957fu5355u8bcdu6362u884c */
}

.motion-tip-close {
  margin-left: 12rpx;
  color: rgba(255, 255, 255, 0.7);
  font-size: 28rpx;
  padding: 0 8rpx;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

/* 相机裁剪容器 - 用于人类视角模拟犬科视觉的视场角 */
.camera-clip-container {
  position: absolute;
  top: 0;
  left: 10%;
  right: 10%;
  height: 100%;
  overflow: hidden;
  z-index: 2;
  border-radius: 0 0 16rpx 16rpx;
}

/* 喵喵视角下的相机裁剪（区别于犬类视角） */
.cat-camera-clip-container {
  position: absolute;
  top: 0;
  left: 5%;
  right: 5%;
  height: 100%;
  overflow: hidden;
  z-index: 2;
  border-radius: 0 0 16rpx 16rpx;
}

/* 喵喵切换到人类视角时的相机裁剪（人类视角下的猫科视野） */
.cat-human-camera-clip-container {
  position: absolute;
  top: 0;
  left: 5%;
  right: 5%;
  height: 100%;
  overflow: hidden;
  z-index: 2;
  border-radius: 0 0 16rpx 16rpx;
}

/* 在裁剪容器中，相机需要恢复完整宽度 */
.camera-clip-container #camera {
  width: 125%;
  left: -12.5%;
}

/* 弧形边缘菜单样式 - 苹果设计风格 */
.arc-vision-selector {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2200; /* 提高z-index，与其他选择器保持一致 */
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.arc-vision-selector.show {
  opacity: 1;
  pointer-events: auto;
}

/* 半透明背景遮罩 */
.arc-selector-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(3px);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 2190; /* 略低于选择器本身 */
}

.arc-vision-selector.show .arc-selector-backdrop {
  opacity: 1;
}

/* 弧形菜单容器 */
.arc-menu-container {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%) translateX(100%);
  width: 220rpx; /* 缩小宽度 */
  height: 420rpx; /* 进一步缩小高度，因为移除了标题 */
  display: flex;
  flex-direction: column;
  justify-content: center; /* 居中对齐 */
  transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.arc-vision-selector.show .arc-menu-container {
  transform: translateY(-50%) translateX(0);
}

/* 菜单标题 */
.arc-menu-title {
  text-align: center;
  padding: 15rpx 0; /* 减少内边距 */
  color: white;
  font-size: 28rpx; /* 缩小字体 */
  font-weight: 500;
  margin-bottom: 15rpx; /* 减少间距 */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 弧形菜单项容器 */
.arc-menu-items {
  position: relative;
  height: 400rpx; /* 调整高度 */
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  padding: 10rpx 0; /* 添加上下内边距 */
}

/* 弧形菜单项 */
.arc-menu-item {
  position: relative;
  display: flex;
  align-items: center;
  height: 80rpx; /* 缩小高度 */
  padding-left: 20rpx; /* 减少内边距 */
  border-radius: 40rpx 0 0 40rpx; /* 调整圆角 */
  margin-bottom: 15rpx; /* 减少间距 */
  background: rgba(66, 82, 128, 0.4);
  backdrop-filter: blur(10px);
  transform: translateX(30rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: -2px 2px 10px rgba(0, 0, 0, 0.2);
  border-left: 3px solid rgba(255, 255, 255, 0.2);
}

/* 激活状态下的菜单项动画 */
.arc-menu-item:active {
  transform: translateX(10rpx) scale(0.98);
}

/* 选中状态 */
.arc-menu-item.selected {
  background: rgba(255, 138, 91, 0.6);
  transform: translateX(0);
  border-left: 3px solid rgba(255, 255, 255, 0.8);
  box-shadow: -4px 4px 15px rgba(255, 138, 91, 0.3), 0 0 20px rgba(255, 138, 91, 0.4);
}

/* 前置选中状态 */
.arc-menu-item.prerequisite-selected {
  background: rgba(255, 138, 91, 0.3);
  transform: translateX(15rpx);
  border-left: 3px solid rgba(255, 255, 255, 0.5);
  box-shadow: -3px 3px 12px rgba(255, 138, 91, 0.2), 0 0 15px rgba(255, 138, 91, 0.2);
}

/* 图标容器 */
.arc-item-icon {
  width: 60rpx; /* 缩小宽度 */
  height: 60rpx; /* 缩小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15rpx; /* 减少间距 */
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.arc-menu-item.selected .arc-item-icon {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
}

.arc-menu-item.prerequisite-selected .arc-item-icon {
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
}

/* 图标文本 */
.arc-item-icon text {
  font-size: 30rpx; /* 缩小字体 */
}

/* 标签文本 */
.arc-item-label {
  color: white;
  font-size: 24rpx; /* 缩小字体 */
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 激活的视角 */
/* 激活的视角 */
.vision-view.active {
  opacity: 1;
  pointer-events: auto;
  animation: fadeInEffect 0.3s ease;
}

@keyframes fadeInEffect {
  from { opacity: 0.7; }
  to { opacity: 1; }
}

/* 控制按钮样式 - 已移除全屏相关 */
.control-panel {
  position: relative;
  z-index: 10;
}

/* 人类视角提示样式 */
.visual-angle-tip {
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.visual-angle-info {
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  border-radius: 36rpx;
  padding: 16rpx 30rpx;
  display: flex;
  align-items: center;
  border: 1px solid rgba(33, 150, 243, 0.3);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  animation: fadeIn 1s ease-in-out;
}

.visual-angle-info text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 切换按钮 */
.switch-btn {
  position: absolute;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px 15px;
  background: rgba(33, 150, 243, 0.7);
  border-radius: 20px;
  color: white;
  font-size: 14px;
  z-index: 10;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

/* 相机切换按钮 */
.camera-switch-btn {
  position: absolute;
  bottom: 70px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px 15px;
  background: rgba(76, 175, 80, 0.7);
  border-radius: 20px;
  color: white;
  font-size: 14px;
  z-index: 10;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

/* 运动视觉按钮 */
.motion-vision-btn {
  position: absolute;
  bottom: 80rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  z-index: 10;
}

.motion-vision-btn.active {
  background-color: rgba(76, 175, 80, 0.8);
}

/* 视觉分析样式 */
.info-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #2196F3;
  border-left: 4px solid #2196F3;
  padding-left: 10px;
}

.sense-card {
  background: rgba(66, 82, 128, 0.8); /* 更亮的卡片背景 */
  padding: 16px;
  border-bottom: 1px solid rgba(255, 138, 91, 0.15);
  transition: all 0.25s ease;
  margin: 0; /* 移除外边距 */
  border-radius: 0; /* 移除圆角 */
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  margin-bottom: 10rpx; /* 只保留底部小间距 */
  border: 1px solid rgba(255, 138, 91, 0.15); /* 更亮的边框 */
}

.sense-title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: white;
  font-size: 16px;
  font-weight: 500;
}

.sense-icon {
  margin-right: 10px;
  font-size: 18px;
  background: rgba(255, 138, 91, 0.1);
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
}

.sense-desc {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.5;
  min-height: 60rpx;
}

.feature-row {
  display: flex;
  margin-bottom: 10rpx;
  align-items: flex-start;
  min-height: 40rpx;
  line-height: 40rpx;
}

.feature-label {
  color: rgba(255, 255, 255, 0.6);
  margin-right: 10rpx;
  min-width: 80rpx;
  line-height: 40rpx;
}

.feature-value {
  color: var(--primary-color);
  font-weight: 500;
  line-height: 40rpx;
  display: flex;
  flex-wrap: wrap;
}

/* 浅色主题下的特征标签和值样式 */
.light-theme-content .feature-label {
  color: rgba(0, 0, 0, 0.6);
}

.light-theme-content .sense-desc {
  color: #333333;
}

/* 扩展描述文字样式 */
.feature-extension {
  margin-top: 12rpx;
  padding: 10rpx 12rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8rpx;
  border-left: 4rpx solid var(--primary-color);
}

.extension-text {
  font-size: 26rpx;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.85);
}

/* 浅色主题下的扩展描述文字样式 */
.light-theme-content .feature-extension {
  background: rgba(0, 0, 0, 0.05);
  border-left: 4rpx solid var(--primary-color);
}

.light-theme-content .extension-text {
  color: #333333;
  font-weight: 500;
}

.light-theme-content .sense-title {
  color: #333333;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* 浅色主题下的标签页容器样式 */
.light-theme-content .tabs-container {
  background: rgba(255, 255, 255, 0.5);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* 浅色主题下的标签样式 */
.light-theme-content .tab {
  color: #333333;
  font-weight: 500;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5); /* 浅色主题下增加文字阴影，提高可读性 */
}

.light-theme-content .active-tab {
  background: rgba(255, 138, 91, 0.2);
  color: #FF8A5B;
  font-weight: 600;
  font-size: 17px; /* 活动标签文字更大 */
}

/* 浅色主题下的卡片样式 */
.light-theme-content .sense-card {
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.progress-bar {
  height: 8px;
  border-radius: 4px;
  margin-top: 16px;
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  overflow: hidden;
  position: relative;
}

.progress-inner {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  transition: width 0.5s ease;
}

/* 调试信息 */
.debug-info {
  position: absolute;
  top: 50px;
  left: 10px;
  color: white;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.5);
  padding: 5px;
  border-radius: 4px;
  z-index: 100;
  display: none; /* 默认隐藏 */
}

/* 设备性能级别指示器 */
.device-performance-indicator {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 10px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.frame-skip-info {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 2px;
  display: flex;
  align-items: center;
}

.frame-skip-info.motion-active {
  color: var(--primary-color);
}

/* 光照模式提示样式 */
.light-status {
  position: absolute;
  top: 0;
  left: 0rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background: rgba(66, 82, 128, 0.8); /* 更亮的提示背景 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 8rpx 16rpx;
  border-radius: 0 0 12rpx 0;
  font-size: 24rpx;
  color: white;
  z-index: 100;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.25);
  border-right: 4rpx solid rgba(255, 138, 91, 0.7);
  border-left: none;
  max-width: 180rpx;
  transform: translateX(0);
  animation: slideInLeft 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 光照模式状态变化动画 */
.light-status.low-light {
  background: rgba(41, 57, 103, 0.85);
  border-right-color: rgba(120, 160, 255, 0.7);
}

.light-status.bright-light {
  background: rgba(103, 82, 41, 0.85);
  border-right-color: rgba(255, 200, 120, 0.7);
}

.light-status.normal-light {
  background: rgba(66, 82, 128, 0.8);
  border-right-color: rgba(255, 138, 91, 0.7);
}

/* 爱宠视角状态指示条组样式 */
.pet-vision-status-group {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  z-index: 101;
  padding: 6rpx 10rpx;
  background: rgba(0, 0, 0, 0);
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}

/* 各个状态指示条项目样式 */
.vision-status-item {
  display: flex;
  align-items: center;
  padding: 6rpx 12rpx;
  margin: 0 4rpx;
  font-size: 24rpx;
  color: white;
  background: rgba(66, 82, 128, 0.4);
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.25);
  border-bottom: 3rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  opacity: 0.6;
  transform: scale(0.95);
}

/* 高亮状态 */
.vision-status-item.active {
  opacity: 1;
  transform: scale(1);
  box-shadow: 0 2rpx 12rpx rgba(255, 255, 255, 0.2);
}

/* 各个状态指示条的特殊样式 */
.vision-status-item.color-vision {
  background: rgba(255, 138, 91, 0.4);
  border-bottom-color: rgba(255, 138, 91, 0.7);
}

.vision-status-item.color-vision.active {
  background: rgba(255, 138, 91, 0.6);
  border-bottom-color: rgba(255, 138, 91, 0.9);
}

.vision-status-item.acuity-vision {
  background: rgba(100, 200, 140, 0.4);
  border-bottom-color: rgba(100, 200, 140, 0.7);
}

.vision-status-item.acuity-vision.active {
  background: rgba(100, 200, 140, 0.6);
  border-bottom-color: rgba(100, 200, 140, 0.9);
}

.vision-status-item.light-vision {
  background: rgba(120, 160, 255, 0.4);
  border-bottom-color: rgba(120, 160, 255, 0.7);
}

.vision-status-item.light-vision.active {
  background: rgba(120, 160, 255, 0.6);
  border-bottom-color: rgba(120, 160, 255, 0.9);
}

.vision-status-item.light-vision.low-light.active {
  background: rgba(41, 57, 103, 0.8);
  border-bottom-color: rgba(120, 160, 255, 0.9);
}

.vision-status-item.light-vision.bright-light.active {
  background: rgba(103, 82, 41, 0.8);
  border-bottom-color: rgba(255, 200, 120, 0.9);
}

.vision-status-item.motion-vision {
  background: rgba(180, 140, 255, 0.4);
  border-bottom-color: rgba(180, 140, 255, 0.7);
}

.vision-status-item.motion-vision.active {
  background: rgba(180, 140, 255, 0.6);
  border-bottom-color: rgba(180, 140, 255, 0.9);
}

/* 状态图标和文本样式 */
.vision-status-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vision-status-text {
  font-weight: 500;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
}

/* 从右到左滑入动画 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 从左到右滑入动画 */
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 光照信息样式 - 优化版 */
.light-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  margin-bottom: 2px;
  font-weight: 500;
  letter-spacing: 0.3px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 运动视觉和夜视模式提示样式 */
.motion-vision-tip {
  position: absolute;
  top: 120rpx; /* 从60rpx修改为120rpx，增加与顶部的距离 */
  left: 50%;
  transform: translateX(-50%) translateY(-20rpx);
  opacity: 0;
  pointer-events: none;
  z-index: 1000;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  width: 80%; /* 从90%修改为80%，减小宽度避免与右侧状态栏重叠 */
  max-width: 600rpx;
}

.motion-tip-show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
  pointer-events: auto;
  animation: fadeInDown 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

.motion-tip-content {
  background: rgba(66, 82, 128, 0.85);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.15);
  max-width: 100%; /* u786eu4fddu4e0du8d85u51fau7236u5bb9u5668 */
  overflow: hidden; /* u9632u6b62u5185u5bb9u6ea2u51fa */
}

.motion-tip-text {
  flex: 1;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.4;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  white-space: normal; /* u5141u8bb8u6587u672cu6362u884c */
  word-wrap: break-word; /* u957fu5355u8bcdu6362u884c */
}

.motion-tip-close {
  margin-left: 20rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.motion-tip-close:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

@keyframes tipBounce {
  0% {
    transform: translateX(-50%) translateY(50rpx);
    opacity: 0;
  }
  50% {
    transform: translateX(-50%) translateY(-10rpx);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

/* 人类视角提示样式 */
.visual-angle-tip {
  position: absolute;
  top: 60rpx;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  z-index: 100;
  animation: fadeInDown 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

.visual-angle-info {
  background: rgba(66, 82, 128, 0.85);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16rpx;
  padding: 16rpx 30rpx;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.15);
  display: flex;
  align-items: center;
  max-width: 85%;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-weight: 500;
  letter-spacing: 0.5rpx;
  line-height: 1.4;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.brightness-row {
  display: flex;
  align-items: center;
}

/* 以下样式可以保留但不会使用 */
.light-indicator {
  display: none;
}

.brightness-indicator {
  display: none;
}

.normal-light .light-info text {
  color: #ffffff;
}

.low-light .light-info text {
  color: var(--primary-color);
  text-shadow: 0 0 6px rgba(255, 138, 91, 0.4);
}

.bright-light .light-info text {
  color: #ffcc00;
  text-shadow: 0 0 6px rgba(255, 204, 0, 0.6);
}

.low-light {
  background: rgba(33, 52, 104, 0.85); /* 低光模式下稍暗但仍比原版亮 */
  border-color: rgba(255, 210, 0, 0.4);
}

.bright-light {
  background: rgba(66, 82, 128, 0.9); /* 强光模式下稍亮 */
  border-color: rgba(255, 204, 0, 0.6);
}

.brightness-value {
  font-family: 'SF Mono', monospace;
  opacity: 0.9;
  font-size: 8px;
  letter-spacing: 0.3px;
}

.low-light .brightness-value {
  color: var(--primary-color);
  opacity: 0.9;
}

.bright-light .brightness-value {
  color: #ffcc00;
  opacity: 0.9;
}

.bright-pixel-ratio {
  font-family: 'SF Mono', monospace;
  opacity: 0.9;
  font-size: 8px;
  letter-spacing: 0.3px;
  margin-left: 8px;
  color: #ffcc00;
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

@keyframes blink {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

/* 标签页样式 */
.tabs {
  display: flex;
  height: 80rpx;
  background: rgba(66, 82, 128, 0.8); /* 更亮的标签背景 */
  border-bottom: 1px solid rgba(255, 138, 91, 0.2); /* 更亮的边框 */
  position: relative;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.6);
  position: relative;
  transition: all 0.3s;
  padding: 0 5rpx;
  letter-spacing: 0.5px;
}

.tab-item.active {
  color: white;
  font-weight: 500;
  background: rgba(255, 138, 91, 0.1); /* 更亮的激活背景 */
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 20%;
  width: 60%;
  height: 3rpx;
  background: var(--primary-color);
  border-radius: 1.5rpx;
}

.tab-content {
  height: calc(100% - 80rpx);
  background: var(--dark-bg);
  overflow: hidden;
  margin: 0; /* 移除间距 */
  border-radius: 0; /* 移除圆角 */
}

.tab-scroll-view {
  height: 100%;
  padding: 0;
}

/* 按钮悬停效果 */
.btn-hover {
  opacity: 0.85;
  transform: translateX(-50%) scale(0.96);
  transition: all 0.1s;
}

/* 标签悬停效果 */
.tab-hover {
  background-color: rgba(255, 255, 255, 0.1); /* 更亮的悬停背景 */
}

/* 卡片悬停效果 */
.card-hover {
  transform: translateY(-1px);
  background: rgba(66, 82, 128, 0.9); /* 更亮的悬停背景 */
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 138, 91, 0.3); /* 更亮的悬停边框 */
}

/* 标签图标 */
.tab-icon {
  margin-right: 6px;
  font-size: 14px;
}

/* 为按钮添加动画效果 */
.switch-btn, .camera-switch-btn, .motion-vision-btn {
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0);
}

/* 为卡片内容添加进入动画 */
.sense-card {
  animation: fadeInUp 0.5s ease forwards;
  opacity: 0;
  transform: translateY(10px);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为每个卡片设置不同的延迟，创建级联效果 */
.sense-card:nth-child(1) {
  animation-delay: 0.1s;
}

.sense-card:nth-child(2) {
  animation-delay: 0.2s;
}

.sense-card:nth-child(3) {
  animation-delay: 0.3s;
}

.sense-card:nth-child(4) {
  animation-delay: 0.4s;
}

/* 标签切换动画 */
.tab-content {
  transition: all 0.3s ease;
  margin: 0; /* 移除间距 */
  border-radius: 0; /* 移除圆角 */
}

/* 视觉参数调整面板 */
.vision-params-panel {
  position: fixed;
  bottom: -50%;
  left: 0;
  right: 0;
  max-height: 50%;
  background: rgba(66, 82, 128, 0.95); /* 更亮的面板背景 */
  backdrop-filter: blur(20px);
  padding: 30rpx;
  border-radius: 24rpx 24rpx 0 0;
  z-index: 2100; /* 提高z-index值，确保高于控制栏和知识库 */
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  box-shadow: 0 -4rpx 24rpx rgba(0, 0, 0, 0.3);
  overflow-y: auto;
  color: white;
  box-sizing: border-box;
  border-top: 1px solid rgba(255, 138, 91, 0.2); /* 更亮的边框 */
}

.vision-params-panel.show {
  bottom: 0;
}

/* 分辨率选择器样式 */
.resolution-selector {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  background: rgba(30, 30, 30, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  width: 80%;
  max-width: 600rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 2200; /* 提高层级，确保在所有其他元素之上 */
}

.resolution-selector.show {
  opacity: 1;
  visibility: visible;
  transform: translate(-50%, -50%) scale(1);
}

.resolution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.resolution-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.close-btn {
  font-size: 40rpx;
  color: #aaa;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-btn:active {
  background: rgba(255, 255, 255, 0.1);
}

.resolution-options {
  margin: 20rpx 0;
}

.resolution-option {
  padding: 24rpx 20rpx;
  margin-bottom: 16rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.resolution-option.selected {
  background: var(--primary-color);
  color: #fff;
}

.resolution-option:active {
  background: rgba(255, 255, 255, 0.15);
}

.resolution-option.selected:active {
  background: var(--primary-color-dark);
}

.resolution-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.resolution-option.selected .resolution-desc {
  color: rgba(255, 255, 255, 0.9);
}

.resolution-note {
  font-size: 24rpx;
  color: #aaa;
  margin-top: 20rpx;
  text-align: center;
  padding: 0 20rpx;
}

.params-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1px solid rgba(255, 138, 91, 0.15);
  margin-bottom: 25rpx;
}

.params-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
}

.params-close {
  font-size: 36rpx;
  padding: 10rpx;
  color: rgba(255, 255, 255, 0.8);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transition: all 0.2s;
}

.params-close:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

.param-item {
  margin-bottom: 25rpx;
}

.param-label {
  font-size: 26rpx;
  margin-bottom: 10rpx;
  display: block;
  color: rgba(255, 255, 255, 0.85);
}

/* 参数描述文字样式 */
.param-desc {
  font-size: 22rpx;
  margin-top: 8rpx;
  display: block;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
  padding-left: 10rpx;
  border-left: 4rpx solid rgba(255, 138, 91, 0.4);
}

/* 重置按钮描述文字样式 */
.reset-desc {
  font-size: 22rpx;
  margin-top: 8rpx;
  display: block;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}

/* 犬种视力参考样式 */
.info-section {
  margin-top: 40rpx;
  border-top: 2rpx solid rgba(255, 138, 91, 0.3);
  padding-top: 30rpx;
}

.breed-info-list {
  margin: 15rpx 0;
}

.breed-info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  padding: 8rpx 10rpx;
  border-radius: 8rpx;
  background-color: rgba(255, 255, 255, 0.05);
}

.breed-info-item:nth-child(odd) {
  background-color: rgba(255, 255, 255, 0.08);
}

.breed-name {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.breed-value {
  color: var(--primary-color);
}

.param-section {
  margin-top: 30rpx;
  margin-bottom: 20rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  padding-top: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--primary-color);
  margin-bottom: 20rpx;
  display: block;
  display: flex;
  align-items: center;
}

.section-title::before {
  margin-right: 8rpx;
}

.reset-btn {
  margin-top: 35rpx;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 16rpx 0;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(255, 138, 91, 0.3);
  transition: all 0.2s;
}

.reset-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(255, 138, 91, 0.2);
}

/* 特性按钮样式 */
.vision-features {
  position: fixed;
  bottom: 150rpx;
  left: 30rpx;
  right: 30rpx;
  display: flex;
  justify-content: center;
  z-index: 900;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 30rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 20rpx;
  padding: 20rpx;
  width: 120rpx;
}

.feature-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 10rpx;
}

.feature-text {
  color: white;
  font-size: 24rpx;
  text-align: center;
}

/* iOS风格半透明浮动控制栏 - 对称设计 */
.ios-control-bar {
  position: fixed; /* 改为fixed定位，确保它在视口中的位置固定 */
  right: 0;
  bottom: 130rpx; /* 默认距离底部有一定距离，避免被知识库遮挡 */
  transform: none;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: transparent; /* 背景完全透明 */
  backdrop-filter: none; /* 移除模糊效果 */
  -webkit-backdrop-filter: none; /* 移除模糊效果 */
  border-radius: 0; /* 移除圆角 */
  padding: 15rpx 10rpx calc(15rpx + env(safe-area-inset-bottom, 10rpx)) 15rpx; /* 保留内边距 */
  box-shadow: none; /* 移除阴影 */
  border: none; /* 移除边框 */
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: 2000; /* 提高z-index，确保始终在知识库上方显示 */
  will-change: transform, opacity;
}

/* 控制栏在知识库展开时的位置调整 */
.analysis-container.expanded ~ .main-content .ios-control-bar {
  bottom: 72vh; /* 调整为略高于知识库展开高度，确保不会重叠 */
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 按钮容器 - 包含图标和文字 */
.ios-btn-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 8rpx 0;
}

/* 按钮样式 - 对称设计 */
.ios-btn {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.5); /* 半透明黑色背景 */
  border: 1px solid rgba(255, 255, 255, 0.25); /* 增加白色边框 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.35); /* 轻微阴影增强层次感 */
  transform: translateZ(0);
  will-change: transform, background;
}

/* 按钮文字说明 */
.ios-btn-label {
  font-size: 20rpx;
  color: white;
  margin-top: 6rpx;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); /* 增强文字阴影，提高可读性 */
  background: rgba(0, 0, 0, 0.4); /* 添加半透明背景 */
  padding: 2rpx 8rpx; /* 添加内边距 */
  border-radius: 10rpx; /* 圆角 */
  line-height: 1.2; /* 调整行高 */
}

/* 显示和隐藏状态 */
.ios-control-bar.visible {
  opacity: 1;
  transform: none;
  pointer-events: auto;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.ios-control-bar.hidden {
  opacity: 0;
  transform: translateX(30rpx);
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 自适应相机区域的定位计算 */
@media screen and (max-height: 700px) {
  .ios-control-bar {
    right: 10rpx; /* 在较低的屏幕上右侧留出一点边距 */
    bottom: 130rpx; /* 保持默认距离底部的位置 */
  }
  
  .analysis-container.expanded ~ .main-content .ios-control-bar {
    bottom: 72vh; /* 略高于知识库展开高度 */
  }
  
  .ios-btn {
    width: 60rpx;
    height: 60rpx;
    background: rgba(0, 0, 0, 0.55); /* 小屏幕上背景稍深一点 */
  }
  
  .ios-btn-label {
    font-size: 18rpx;
    padding: 1rpx 6rpx; /* 小尺寸屏幕上减小内边距 */
  }
}

/* 特别针对华为Mate30等中等屏幕尺寸的手机适配 */
@media screen and (min-height: 701px) and (max-height: 799px) {
  .ios-control-bar {
    right: 10rpx; /* 中等屏幕上右侧留出一点边距 */
  }
  
  .analysis-container.expanded ~ .main-content .ios-control-bar {
    bottom: 72vh; /* 确保在中等屏幕手机上不会与知识库重叠 */
  }
  
  .ios-btn {
    width: 65rpx;
    height: 65rpx;
  }
}

/* 新增：针对大屏幕的适配 */
@media screen and (min-height: 800px) {
  .ios-control-bar {
    right: 15rpx; /* 大屏幕上右侧留出更多边距 */
    padding-bottom: calc(env(safe-area-inset-bottom, 15rpx) + 10rpx); /* 在大屏幕上增加底部间距 */
  }
  
  .analysis-container.expanded ~ .main-content .ios-control-bar {
    bottom: 72vh; /* 确保在大屏幕上不会与知识库重叠 */
  }
  
  .ios-btn {
    width: 75rpx; /* 大屏幕上按钮稍大一些 */
    height: 75rpx;
  }
  
  .ios-btn-label {
    font-size: 22rpx; /* 大屏幕上文字稍大 */
  }
}

/* 图标样式 */
.ios-icon {
  font-size: 36rpx;
  color: white;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5); /* 增强图标阴影 */
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  transform: translateZ(0);
  will-change: transform;
}

/* 按钮悬停效果 */
.ios-btn-hover {
  transform: scale(0.92) translateZ(0);
  background: rgba(255, 138, 91, 0.5); /* 更亮的悬停背景 */
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.4), 0 0 15rpx rgba(255, 138, 91, 0.3); /* 更强的光晕效果 */
  border-color: rgba(255, 255, 255, 0.4); /* 更亮的边框 */
}

/* 激活状态 */
.ios-btn.active {
  background: rgba(255, 138, 91, 0.3);
}

/* 按钮悬停效果 */
.ios-btn-hover {
  transform: scale(0.92) translateZ(0);
  background: rgba(255, 138, 91, 0.2);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 138, 91, 0.3);
}

/* 激活按钮光效果动画 */
.ios-btn-hover::after {
  top: 100%;
  left: 100%;
}

/* 相机加载提示样式 */
.camera-loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.camera-loading-container.show {
  opacity: 1;
  pointer-events: auto;
}

.camera-loading-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(66, 82, 128, 0.4);
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-width: 80%;
}

.loading-ring {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.1);
  border-top: 4rpx solid rgba(255, 138, 91, 0.9);
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
  margin-bottom: 20rpx;
  position: relative;
}

.loading-dot {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background: #FF8A5B;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  margin-top: -6rpx;
  margin-left: -6rpx;
  box-shadow: 0 0 10rpx rgba(255, 138, 91, 0.8);
}

.camera-loading-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8rpx;
  font-weight: 500;
}

.loading-dots {
  display: flex;
  justify-content: center;
}

.loading-dots .dot {
  font-size: 28rpx;
  color: rgba(255, 138, 91, 0.9);
  animation: dotPulse 1.4s infinite;
  margin: 0 2rpx;
}

.loading-dots .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dots .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes dotPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* 圆形按钮 - 尺寸优化 */
.circle-btn {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 8rpx;
  background: rgba(66, 82, 128, 0.7);
  transition: all 0.2s ease;
  position: relative;
  border: 1rpx solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.btn-icon {
  font-size: 36rpx;
  margin-bottom: 2rpx;
}

.btn-label {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
  font-weight: 400;
}

/* 按钮悬停效果 */
.btn-hover {
  transform: scale(0.95);
  background: rgba(66, 82, 128, 0.9);
  box-shadow: 0 0 15rpx rgba(100, 149, 237, 0.5);
}

.control-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(66, 82, 128, 0.75); /* 更亮的控制按钮背景 */
  backdrop-filter: blur(10px);
  border-radius: 12rpx;
  padding: 8rpx;
  width: 70rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.15); /* 更亮的边框 */
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.control-icon {
  font-size: 24rpx;
  margin-bottom: 4rpx;
  color: white;
}

.control-text {
  color: white;
  font-size: 16rpx;
  text-align: center;
}

.control-hover {
  transform: scale(0.95);
  background-color: rgba(255, 138, 91, 0.3); /* 更亮的悬停状态 */
}

.main-control {
  background: rgba(255, 138, 91, 0.25); /* 更亮的主控制按钮背景 */
  border: 1rpx solid rgba(255, 138, 91, 0.3); /* 更亮的边框 */
}

.active-control {
  background: rgba(255, 138, 91, 0.25); /* 更亮的激活状态 */
  border: 1rpx solid rgba(255, 138, 91, 0.4); /* 更亮的边框 */
}

/* 横屏模式适配 */
@media screen and (orientation: landscape) {
  .camera-controls {
    flex-direction: column;
    right: 0rpx;
    bottom: 30rpx;
    left: auto;
    transform: none;
    gap: 12rpx;
  }
  
  .tabs {
    height: 70rpx;
  }
  
  .tab-content {
    height: calc(100% - 70rpx);
  }
}

/* 小屏幕手机适配 */
@media screen and (max-height: 600px) {
  .camera-controls {
    bottom: 15rpx;
    right: 0rpx;
    gap: 6rpx;
  }
  
  .control-item {
    padding: 6rpx;
    width: 60rpx;
  }
  
  .control-icon {
    font-size: 22rpx;
  }
  
  .control-text {
    font-size: 14rpx;
  }
  
  .light-status {
    padding: 2px 4px;
    font-size: 8px;
    max-width: 70px;
  }
  
  .brightness-value {
    font-size: 7px;
  }
  
  /* 小屏幕设备的状态指示条组适配 */
  .pet-vision-status-group {
    padding: 4rpx 6rpx;
  }
  
  .vision-status-item {
    padding: 3rpx 8rpx;
    font-size: 18rpx;
    margin: 0 2rpx;
    border-radius: 12rpx;
  }
  
  .vision-status-icon {
    margin-right: 3rpx;
    font-size: 18rpx;
  }
  
  .vision-status-text {
    font-size: 18rpx;
  }
}

.normal-light .brightness-indicator {
  background: var(--secondary-color);
  box-shadow: 0 0 6px rgba(120, 194, 173, 0.8);
}

.low-light .brightness-indicator {
  background: var(--primary-color);
  box-shadow: 0 0 8px rgba(255, 138, 91, 0.8);
  animation: blink 1.5s infinite;
}

/* 标签页容器 */
.tabs-container {
  display: flex;
  width: 100%;
  border-bottom: 1px solid var(--card-bg);
  background: rgba(0, 0, 0, 0.1);
  transition: border-color 0.5s ease; /* 添加主题过渡效果 */
}

.tab {
  flex: 1;
  text-align: center;
  padding: 14px 0;
  font-size: 16px;
  color: var(--text-secondary);
  position: relative;
  transition: color 0.3s, background-color 0.3s ease; /* 添加主题过渡效果 */
  font-weight: 500; /* 增加默认字体粗细 */
  letter-spacing: 1px; /* 增加字间距，提高可读性 */
}

.tab.active-tab {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 17px; /* 活动标签文字更大 */
  background: rgba(255, 138, 91, 0.1); /* 活动标签背景色 */
}

.tab.active-tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 3px;
  background: var(--primary-color);
  border-radius: 2px;
}

/* 标签内容区域 */
.tab-content {
  flex: 1;
  background: var(--card-bg);
  border-radius: 0; /* 移除圆角 */
  margin: 0; /* 移除间距 */
  overflow: hidden;
  transition: background 0.5s ease; /* 添加主题过渡效果 */
}

.content-section {
  height: 100%;
  opacity: 1;
  padding: 0; /* 确保没有内边距 */
  margin: 0; /* 确保没有外边距 */
  width: 100%; /* 确保宽度为100% */
  box-sizing: border-box; /* 确保边框包含在宽度内 */
  transition: opacity 0.3s ease; /* 添加内容切换过渡效果 */
}

.content-section[hidden] {
  opacity: 0;
  height: 0;
  overflow: hidden;
}

/* 相机加载提示样式 */
.camera-loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
  z-index: 50;
}

.camera-loading-box {
  padding: 30rpx;
  background: rgba(66, 82, 128, 0.9);
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);
  border: 1rpx solid rgba(255, 138, 91, 0.3);
}

.camera-loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.camera-loading-text {
  font-size: 28rpx;
  color: white;
}

/* 相机错误提示样式 */
.camera-error-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
  z-index: 50;
}

.camera-error-box {
  padding: 30rpx;
  background: rgba(66, 82, 128, 0.9);
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);
  border: 1rpx solid rgba(255, 138, 91, 0.3);
  max-width: 80%;
}

.camera-error-icon {
  font-size: 48rpx;
  margin-bottom: 20rpx;
}

.camera-error-text {
  font-size: 32rpx;
  color: white;
  font-weight: 500;
  margin-bottom: 10rpx;
  text-align: center;
}

.camera-error-tip {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

.param-info {
  padding: 10rpx 20rpx;
  margin-bottom: 15rpx;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8rpx;
}

.feature-explanation {
  font-size: 24rpx;
  color: var(--text-color-secondary);
  line-height: 1.5;
  display: block;
  padding: 10rpx 0;
}

/* performance-options 样式 */
.performance-options {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 10rpx 0;
  width: 100%;
}

.performance-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(40, 44, 52, 0.5);
  color: #cccccc;
  padding: 15rpx 10rpx;
  border-radius: 8rpx;
  margin: 0 6rpx;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.performance-option.active {
  background: rgba(var(--primary-color-rgb), 0.15);
  color: var(--primary-color);
  border: 1px solid rgba(var(--primary-color-rgb), 0.5);
  box-shadow: 0 0 8rpx rgba(var(--primary-color-rgb), 0.3);
}

.performance-option text {
  font-size: 28rpx;
  font-weight: 500;
}

.performance-option .option-desc {
  font-size: 20rpx;
  opacity: 0.7;
  margin-top: 4rpx;
}

/* 特征扩展样式 */
.feature-extension {
  margin-top: 10rpx;
  padding: 10rpx 16rpx;
  background: rgba(255, 138, 91, 0.12);
  border-radius: 8rpx;
  border-left: 4rpx solid var(--primary-color);
}

.extension-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.6;
  text-align: justify;
  font-weight: 400;
  letter-spacing: 0.2px;
}

/* 性能信息显示样式 */
.performance-info {
  position: absolute;
  top: 10px;
  left: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
  z-index: 100;
}

.performance-column {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.performance-level, .frame-skip-info, .resolution-info, .fps-info, .night-vision-text {
  background: rgba(66, 82, 128, 0.8);
  backdrop-filter: blur(10px);
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 138, 91, 0.2);
}

.resolution-info, .fps-info {
  font-family: 'SF Mono', monospace;
  letter-spacing: 0.3px;
}

/* 为标签内的滚动区域添加额外样式，确保卡片完全对齐 */
.tab-scroll-view .sense-card:first-child {
  margin-top: 0; /* 移除第一个卡片的顶部外边距 */
}

.sense-card:last-child {
  margin-bottom: 0; /* 移除最后一个卡片的底部外边距 */
}

/* 新增：温馨提示文本样式 */
.warning-text {
  margin-top: 10rpx; /* 与上面的文本增加间距 */
  color: #FF6B6B; /* 使用一个醒目的红色 */
  font-size: 13px;
  font-weight: 500; /* 稍微加粗 */
  padding: 8rpx 0; /* 上下增加一点内边距 */
}

/* 夜视模式提示样式 */
.night-tip {
  background: rgba(41, 57, 103, 0.85) !important;
  border: 1px solid rgba(100, 149, 237, 0.5) !important;
}

.night-tip .motion-tip-icon {
  color: #6495ED;
  animation: glow 1.5s infinite alternate;
}

@keyframes glow {
  from { text-shadow: 0 0 2px #6495ED, 0 0 4px #6495ED; }
  to { text-shadow: 0 0 4px #6495ED, 0 0 8px #6495ED; }
}

.motion-tip-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  color: var(--primary-color);
  animation: pulse 1.5s infinite ease-in-out;
}

.motion-tip-text {
  flex: 1;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.4;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  white-space: normal; /* u5141u8bb8u6587u672cu6362u884c */
  word-wrap: break-word; /* u957fu5355u8bcdu6362u884c */
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

/* 圆环形视觉模式选择器 */
.circular-vision-selector {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2200; /* 提高层级，确保在所有其他元素之上 */
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  /* 减少底部内边距，整体更紧凑 */
  padding-bottom: 10rpx;
}

.vision-mode-hierarchy-info {
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 10rpx 15rpx;
  border-radius: 20rpx;
  margin-bottom: 15rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 500;
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.circular-vision-selector.show {
  opacity: 1;
  visibility: visible;
}

.circular-vision-selector.fade-out {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.circular-selector-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.65);
  backdrop-filter: blur(6px);
}

.circular-menu-container {
  position: relative;
  z-index: 201;
  width: 85%;
  max-width: 650rpx;
  /* 增加底部内边距，容纳提示文本 */
  padding-bottom: 40rpx;
}

.vision-mode-hierarchy-info {
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 12rpx 20rpx;
  border-radius: 25rpx;
  margin-bottom: 20rpx;
  text-align: center;
  font-size: 26rpx;
  font-weight: 500;
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

/* 改进后的网格布局，增加间距 */
.circular-menu-items.improved-layout {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, auto);
  gap: 20rpx;
  width: 100%;
  /* 调整顶部边距 */
  margin-top: 15rpx;
  /* 与下方提示的间距 */
  margin-bottom: 20rpx;
}

.circular-menu-item {
  position: relative;
  background: rgba(40, 44, 52, 0.8);
  border-radius: 24rpx;
  padding: 20rpx 15rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.25);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  /* 减少最小高度使整体更紧凑 */
  min-height: 130rpx;
  /* 减少最小宽度 */
  min-width: 180rpx;
}

/* 视觉模式详情 */
.vision-mode-detail {
  position: relative;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20rpx;
  width: 90%;
  margin: 20rpx auto 0;
  /* 减少内边距和高度 */
  padding: 24rpx 24rpx 30rpx;
  min-height: 120rpx;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.15);
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
  /* 增加Z轴层级，确保在底部优先显示 */
  z-index: 2210; /* 比选择器更高一点 */
}

.show .vision-mode-detail {
  opacity: 1;
  transform: translateY(0);
}

/* 菜单项细节样式 */
.circular-item-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.circular-item-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 级别徽章 */
.level-badge {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 44rpx;
  height: 44rpx;
  background: rgba(255, 255, 255, 0.35);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #fff;
  font-weight: bold;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.current-mode-detail.improved-detail {
  padding: 20rpx;
  min-height: auto;
}

.mode-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 10rpx;
  text-align: center;
}

.mode-desc {
  font-size: 26rpx;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
}

/* 视觉模式选择引导提示 */
.vision-select-guide {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(20rpx);
  margin-bottom: 16rpx;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: 950;
  pointer-events: none;
}

.vision-select-guide.show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
  pointer-events: auto;
}

.vision-select-guide .guide-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-left: 16rpx solid transparent;
  border-right: 16rpx solid transparent;
  border-top: 16rpx solid rgba(255, 100, 100, 0.9);
  bottom: -12rpx;
  left: 50%;
  transform: translateX(-50%);
  filter: drop-shadow(0 4rpx 4rpx rgba(0, 0, 0, 0.2));
}

/* 添加动画效果 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes float {
  0% { transform: translateY(0); }
  50% { transform: translateY(-6rpx); }
  100% { transform: translateY(0); }
}

.vision-select-guide.show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
  pointer-events: auto;
}

.vision-select-guide .guide-content {
  background: linear-gradient(135deg, rgba(255, 138, 91, 0.9), rgba(255, 100, 100, 0.9));
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 14rpx 24rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
  text-align: center;
}

.vision-select-guide .guide-content text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5rpx;
}

.vision-select-guide .guide-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-left: 16rpx solid transparent;
  border-right: 16rpx solid transparent;
  border-top: 16rpx solid rgba(255, 100, 100, 0.9);
  bottom: -12rpx;
  left: 50%;
  transform: translateX(-50%);
  filter: drop-shadow(0 4rpx 4rpx rgba(0, 0, 0, 0.2));
}

/* 视觉模式按钮容器 */
.vision-mode-btn-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 视觉模式选择引导提示 */
.vision-select-guide {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(20rpx);
  margin-bottom: 16rpx;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: 950;
  pointer-events: none;
}

.vision-select-guide.show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
  pointer-events: auto;
}

.vision-select-guide .guide-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-left: 16rpx solid transparent;
  border-right: 16rpx solid transparent;
  border-top: 16rpx solid rgba(255, 100, 100, 0.9);
  bottom: -12rpx;
  left: 50%;
  transform: translateX(-50%);
  filter: drop-shadow(0 4rpx 4rpx rgba(0, 0, 0, 0.2));
}

/* 添加动画效果 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes float {
  0% { transform: translateY(0); }
  50% { transform: translateY(-6rpx); }
  100% { transform: translateY(0); }
}

.vision-select-guide .guide-content {
  background: linear-gradient(135deg, rgba(255, 138, 91, 0.9), rgba(255, 100, 100, 0.9));
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 14rpx 24rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
  text-align: center;
}

.vision-select-guide .guide-content text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5rpx;
}

/* 注释掉横屏适配代码，根据需求暂不考虑横屏 
@media screen and (orientation: landscape) {
  .ios-control-bar {
    right: 10rpx;
    bottom: 130rpx;
    padding-bottom: 0; 
    padding-right: env(safe-area-inset-right, 10rpx);
  }
  
  .analysis-container.expanded ~ .main-content .ios-control-bar {
    bottom: 130rpx;
    right: 35vw;
  }
}
*/

/* 包含功能徽章 - 保留但简化样式 */
.includes-badge {
  position: absolute;
  bottom: 10rpx;
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(0, 0, 0, 0.4);
  padding: 3rpx 10rpx;
  border-radius: 14rpx;
}

/* 不同级别的模式样式 */
.circular-menu-item.level-1 {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(20, 60, 120, 0.7));
  border: 2px solid rgba(100, 140, 200, 0.3);
}
.circular-menu-item.level-1 .level-badge {
  background: rgba(100, 140, 200, 0.4);
}

.circular-menu-item.level-2 {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(20, 100, 60, 0.7));
  border: 2px solid rgba(100, 200, 140, 0.3);
}
.circular-menu-item.level-2 .level-badge {
  background: rgba(100, 200, 140, 0.4);
}

.circular-menu-item.level-3 {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(80, 40, 120, 0.7));
  border: 2px solid rgba(140, 100, 200, 0.3);
}
.circular-menu-item.level-3 .level-badge {
  background: rgba(140, 100, 200, 0.4);
}

.circular-menu-item.level-4 {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(150, 30, 30, 0.7));
  border: 2px solid rgba(200, 100, 100, 0.3);
}
.circular-menu-item.level-4 .level-badge {
  background: rgba(200, 100, 100, 0.4);
}

/* 选中样式 - 简化但保留 */
.circular-menu-item.selected {
  transform: scale(1.03);
  box-shadow: 0 0 16rpx rgba(255, 255, 255, 0.4);
  border-width: 2px;
}

/* 选中状态下的级别徽章样式增强 */
.circular-menu-item.selected .level-badge {
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 8rpx rgba(255, 255, 255, 0.3);
}

.circular-menu-item.level-1.selected {
  border-color: rgba(100, 140, 255, 0.8);
}
.circular-menu-item.level-1.selected .level-badge {
  background: rgba(100, 140, 255, 0.6);
}

.circular-menu-item.level-2.selected {
  border-color: rgba(100, 255, 140, 0.8);
}
.circular-menu-item.level-2.selected .level-badge {
  background: rgba(100, 255, 140, 0.6);
}

.circular-menu-item.level-3.selected {
  border-color: rgba(180, 140, 255, 0.8);
}
.circular-menu-item.level-3.selected .level-badge {
  background: rgba(180, 140, 255, 0.6);
}

.circular-menu-item.level-4.selected {
  border-color: rgba(255, 100, 100, 0.8);
}
.circular-menu-item.level-4.selected .level-badge {
  background: rgba(255, 100, 100, 0.6);
}

/* 当前选择的模式提示 */
.mode-selected-tip {
  text-align: center;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 16rpx;
  padding: 12rpx 25rpx;
  margin: 0 auto;
  margin-bottom: 15rpx;
  max-width: 80%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  /* 添加动画效果 */
  animation: fadeIn 0.3s ease-in-out;
}

.mode-selected-tip text {
  color: rgba(255, 255, 255, 0.95);
  font-size: 26rpx;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 为不同模式的提示添加不同的背景色调 */
.mode-tip-1 {
  background: rgba(20, 60, 120, 0.5);
  border-color: rgba(100, 140, 200, 0.3);
}

.mode-tip-2 {
  background: rgba(20, 100, 60, 0.5);
  border-color: rgba(100, 200, 140, 0.3);
}

.mode-tip-3 {
  background: rgba(80, 40, 120, 0.5);
  border-color: rgba(140, 100, 200, 0.3);
}

.mode-tip-4 {
  background: rgba(150, 30, 30, 0.5);
  border-color: rgba(200, 100, 100, 0.3);
}

/* ==================== CAM硬件相机样式 ==================== */

/* CAM按钮激活状态 */
.ios-btn.cam-active {
  background: rgba(50, 205, 50, 0.2) !important;
  border: 2px solid rgba(50, 205, 50, 0.6);
  animation: camPulse 2s ease-in-out infinite;
}

@keyframes camPulse {
  0%, 100% { 
    box-shadow: 0 0 0 0 rgba(50, 205, 50, 0.4); 
  }
  50% { 
    box-shadow: 0 0 0 6px rgba(50, 205, 50, 0); 
  }
}

/* CAM图片容器 */
.cam-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 12rpx;
  overflow: hidden;
}

/* CAM图片样式 */
.cam-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.1s ease-in-out;
  border-radius: 12rpx;
}

.cam-image.cam-active {
  opacity: 1;
  z-index: 2;
}

.cam-image.cam-hidden {
  opacity: 0;
  z-index: 1;
}

/* CAM状态指示器 */
.cam-status-indicator {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  z-index: 10;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.cam-status-indicator.connecting {
  background: rgba(255, 165, 0, 0.8);
  animation: connecting 1.5s ease-in-out infinite;
}

.cam-status-indicator.connected {
  background: rgba(50, 205, 50, 0.8);
}

.cam-status-indicator.error {
  background: rgba(220, 20, 60, 0.8);
  animation: errorBlink 1s ease-in-out infinite;
}

@keyframes connecting {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes errorBlink {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 0.4; }
}

/* CAM加载容器 */
.cam-loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12rpx;
}

.cam-loading-container.show {
  opacity: 1;
}

.cam-loading-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60rpx 40rpx;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 20rpx;
  backdrop-filter: blur(20rpx);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cam-loading-ring {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(50, 205, 50, 0.3);
  border-top: 4rpx solid rgba(50, 205, 50, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.cam-loading-dot {
  width: 12rpx;
  height: 12rpx;
  background: rgba(50, 205, 50, 0.8);
  border-radius: 50%;
  margin-top: -30rpx;
  animation: dot-move 1s linear infinite;
}

.cam-loading-text {
  color: rgba(50, 205, 50, 0.9);
  font-size: 32rpx;
  font-weight: 600;
  margin-top: 30rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.cam-loading-desc {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
  margin-top: 16rpx;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.5);
}

@keyframes dot-move {
  0% { transform: rotate(0deg) translateX(30rpx) rotate(0deg); }
  100% { transform: rotate(360deg) translateX(30rpx) rotate(-360deg); }
}

/* CAM错误容器 */
.cam-error-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 110;
  border-radius: 12rpx;
}

.cam-error-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60rpx 40rpx;
  background: rgba(20, 20, 20, 0.9);
  border-radius: 20rpx;
  backdrop-filter: blur(20rpx);
  border: 2px solid rgba(220, 20, 60, 0.3);
  max-width: 80%;
}

.cam-error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  animation: errorShake 0.5s ease-in-out;
}

.cam-error-text {
  color: rgba(220, 20, 60, 0.9);
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.cam-error-tip {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
  margin-bottom: 40rpx;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.5);
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* CAM错误操作按钮区 */
.cam-error-actions {
  display: flex;
  gap: 30rpx;
  margin-top: 20rpx;
}

.cam-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 30rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  min-width: 120rpx;
  border: 2px solid transparent;
}

.cam-action-btn.retry-btn {
  background: rgba(50, 205, 50, 0.15);
  border-color: rgba(50, 205, 50, 0.3);
}

.cam-action-btn.retry-btn:active {
  background: rgba(50, 205, 50, 0.25);
  transform: scale(0.95);
}

.cam-action-btn.back-btn {
  background: rgba(70, 130, 180, 0.15);
  border-color: rgba(70, 130, 180, 0.3);
}

.cam-action-btn.back-btn:active {
  background: rgba(70, 130, 180, 0.25);
  transform: scale(0.95);
}

.cam-action-btn .action-btn-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.cam-action-btn .action-btn-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* CAM模式下的视觉处理优化 */
.cam-image-container .vision-view {
  /* 确保CAM图片在视觉处理模式下正确显示 */
  border-radius: 12rpx;
  overflow: hidden;
}

/* ==================== 相机选择器样式 ==================== */

/* 相机选择器遮罩层 */
.camera-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.camera-selector-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* 相机选择器面板 */
.camera-selector-panel {
  width: 80%;
  max-width: 600rpx;
  background: rgba(40, 60, 90, 0.95);
  border-radius: 24rpx;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transform: scale(0.9) translateY(50rpx);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.camera-selector-overlay.show .camera-selector-panel {
  transform: scale(1) translateY(0);
}

/* 浅色主题下的面板样式 */
.light-theme-content .camera-selector-panel {
  background: rgba(250, 250, 250, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* 选择器头部 */
.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.light-theme-content .selector-header {
  border-bottom-color: rgba(0, 0, 0, 0.1);
}

.selector-title {
  font-size: 36rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.light-theme-content .selector-title {
  color: rgba(0, 0, 0, 0.9);
}

.selector-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.light-theme-content .selector-close {
  color: rgba(0, 0, 0, 0.6);
  background: rgba(0, 0, 0, 0.05);
}

.selector-close:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.9);
}

.light-theme-content .selector-close:active {
  background: rgba(0, 0, 0, 0.1);
}

/* 相机选项容器 */
.camera-options {
  padding: 20rpx 40rpx;
}

/* 相机选项 */
.camera-option {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
}

.light-theme-content .camera-option {
  background: rgba(0, 0, 0, 0.05);
}

.camera-option:last-child {
  margin-bottom: 0;
}

.camera-option:active {
  transform: scale(0.98);
}

.camera-option.active {
  background: rgba(255, 138, 91, 0.15);
  border-color: rgba(255, 138, 91, 0.3);
  box-shadow: 0 4px 16px rgba(255, 138, 91, 0.2);
}

.light-theme-content .camera-option.active {
  background: rgba(255, 138, 91, 0.1);
  border-color: rgba(255, 138, 91, 0.3);
}

.camera-option.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 选项图标 */
.option-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  margin-right: 24rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.1);
}

.light-theme-content .option-icon {
  background: rgba(0, 0, 0, 0.05);
}

.camera-option.active .option-icon {
  background: rgba(255, 138, 91, 0.2);
}

/* 选项内容 */
.option-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.option-name {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8rpx;
}

.light-theme-content .option-name {
  color: rgba(0, 0, 0, 0.9);
}

.option-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
}

.light-theme-content .option-desc {
  color: rgba(0, 0, 0, 0.6);
}

.option-note {
  font-size: 22rpx;
  color: rgba(255, 193, 7, 0.8);
  margin-top: 8rpx;
  font-style: italic;
}

.light-theme-content .option-note {
  color: rgba(255, 152, 0, 0.8);
}

/* 选项状态 */
.option-status {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.status-selected {
  color: rgba(50, 205, 50, 0.9);
  font-size: 32rpx;
  font-weight: bold;
}

.status-connected {
  color: rgba(50, 205, 50, 0.9);
  font-size: 24rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* 选择器底部 */
.selector-footer {
  padding: 20rpx 40rpx 40rpx;
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.light-theme-content .selector-footer {
  border-top-color: rgba(0, 0, 0, 0.1);
}

.selector-tip {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.5);
  line-height: 1.4;
}

.light-theme-content .selector-tip {
  color: rgba(0, 0, 0, 0.5);
}

/* 相机选择器的响应式适配 */
@media (max-width: 375px) {
  .camera-selector-panel {
    width: 90%;
  }
  
  .camera-option {
    padding: 24rpx 16rpx;
  }
  
  .option-icon {
    width: 64rpx;
    height: 64rpx;
    font-size: 40rpx;
    margin-right: 20rpx;
  }
  
  .option-name {
    font-size: 28rpx;
  }
  
  .option-desc {
    font-size: 22rpx;
  }
}

/* ==================== 硬件相机模式提示样式 ==================== */

/* 硬件相机模式提示 */
.cam-mode-tip {
  position: absolute;
  top: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  animation: slideDown 0.5s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.cam-mode-info {
  background: rgba(255, 193, 7, 0.9);
  border-radius: 24rpx;
  padding: 20rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4px 16px rgba(255, 193, 7, 0.3);
  border: 1px solid rgba(255, 193, 7, 0.5);
  backdrop-filter: blur(10px);
  min-width: 300rpx;
}

.light-theme-content .cam-mode-info {
  background: rgba(255, 152, 0, 0.9);
  box-shadow: 0 4px 16px rgba(255, 152, 0, 0.3);
  border-color: rgba(255, 152, 0, 0.5);
}

.cam-mode-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.cam-mode-text {
  font-size: 28rpx;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.9);
  margin-bottom: 6rpx;
  text-align: center;
}

.cam-mode-desc {
  font-size: 22rpx;
  color: rgba(0, 0, 0, 0.7);
  text-align: center;
  line-height: 1.3;
}

/* 硬件相机模式提示的响应式适配 */
@media (max-width: 375px) {
  .cam-mode-tip {
    top: 80rpx;
  }
  
  .cam-mode-info {
    padding: 16rpx 24rpx;
    min-width: 260rpx;
  }
  
  .cam-mode-icon {
    font-size: 28rpx;
  }
  
  .cam-mode-text {
    font-size: 24rpx;
  }
  
  .cam-mode-desc {
    font-size: 20rpx;
  }
}