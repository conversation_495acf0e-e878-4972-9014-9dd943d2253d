/**
 * 页面生命周期管理器
 * 负责处理页面的生命周期事件和状态管理
 */

// 全局变量
let fpsUpdateInterval = null;
let lastFrameTime = 0;
let frameCount = 0;

const lifecycleManager = {
  
  /**
   * 页面加载初始化
   */
  onLoad: function(page, options) {
    console.log('页面加载，开始初始化');
    
    // 保存页面实例到全局变量
    global.page = page;
    
    // 获取品种信息
    const app = getApp();
    const breedInfo = app.globalData.selectedBreed;
    const breedType = app.globalData.breedType;
    
    if (breedInfo) {
      console.log('获取到品种信息:', breedInfo);
      
      // 设置品种名称和详情
      page.setData({
        breedName: breedInfo.name,
        breedDetails: breedInfo
      });
      
      // 根据品种类型设置视觉参数
      page.setBreedVisualAcuity(breedInfo.name, breedType);
      
      // 更新视觉标签页数据
      const uiController = require('./ui-controller');
      uiController.updateVisionTabsData(breedInfo, page);
      
      // 设置动物类型标志
      const isCat = breedType === 'cat';
      page.setData({
        'features.isCat': isCat
      });
      
      console.log('设置动物类型:', isCat ? '猫科' : '犬科');
    } else {
      console.warn('未获取到品种信息，使用默认设置');
    }
    
    // 初始化相机类型
    page.initializeCameraType();
    
    // 检查是否显示视觉选择引导
    page.showVisionSelectGuide();
    
    // 注册应用显示/隐藏事件监听
    wx.onAppShow(page._handleAppShow.bind(page));
    wx.onAppHide(page._handleAppHide.bind(page));
    
    // 初始化CAM管理器
    page.initCamManager();
    
    // 检查是否显示视觉引导
    page.checkShowVisionGuide();
  },

  /**
   * 页面就绪
   */
  onReady: function(page) {
    console.log('页面就绪，准备完成');
    
    // 更新主题
    page.updateTheme();
    
    // 注册主题变化回调
    page.registerThemeCallback();
    
    // 启动FPS计数器
    page.startFPSCounter();
    
    // 初始化相机
    setTimeout(() => {
      page.initCamera();
    }, 500);
  },

  /**
   * 页面显示
   */
  onShow: function(page) {
    console.log('页面显示');
    
    // 重新注册主题回调
    page.registerThemeCallback();
    
    // 更新主题
    page.updateTheme();
    
    // 如果相机已初始化但未启动，重新启动
    if (page.cameraCtx && !page.data.cameraLoading) {
      console.log('重新启动相机');
      page.initCamera();
    }
    
    // 如果是CAM模式，检查连接状态
    if (page.data.camMode) {
      console.log('CAM模式下页面显示，检查连接状态');
      // 可以在这里添加CAM状态检查逻辑
    }
  },

  /**
   * 页面隐藏
   */
  onHide: function(page) {
    console.log('视觉模拟页面隐藏');
    
    // 注销主题回调
    page.unregisterThemeCallback();
    
    // 停止FPS计数器
    if (fpsUpdateInterval) {
      clearInterval(fpsUpdateInterval);
      fpsUpdateInterval = null;
    }
    
    // 暂停相机（但不完全停止，以便快速恢复）
    if (page.cameraCtx) {
      console.log('页面隐藏，暂停相机');
      // 这里可以添加相机暂停逻辑
    }
  },

  /**
   * 页面卸载
   */
  onUnload: function(page) {
    console.log('视觉模拟页面卸载');
    
    // 注销主题回调
    page.unregisterThemeCallback();
    
    // 停止FPS计数器
    if (fpsUpdateInterval) {
      clearInterval(fpsUpdateInterval);
      fpsUpdateInterval = null;
    }
    
    // 清理WebGL资源
    if (global.webglContext) {
      try {
        const webglRenderer = require('./webgl-renderer');
        webglRenderer.cleanup(global.webglContext);
        global.webglContext = null;
      } catch (error) {
        console.error('清理WebGL资源时出错:', error);
      }
    }
    
    // 停止相机
    if (page.cameraCtx) {
      try {
        page.cameraCtx.stop();
        page.cameraCtx = null;
      } catch (error) {
        console.error('停止相机时出错:', error);
      }
    }
    
    // 如果是CAM模式，清理CAM资源
    if (page.data.camMode) {
      page.disableCamMode();
    }
    
    // 强制内存清理
    page._forceMemoryCleanup();
    
    // 清理全局变量
    global.page = null;
    global.cameraCtx = null;
    
    console.log('页面卸载完成，资源已清理');
  },

  /**
   * 应用显示处理
   */
  handleAppShow: function(page) {
    console.log('小程序返回前台显示');
    page._pageHidden = false;
    
    // 如果相机出现错误，尝试重新初始化
    if (page.data.cameraError) {
      console.log('检测到相机错误，尝试重新初始化');
      setTimeout(() => {
        page.retryCameraInit();
      }, 1000);
    }
    
    // 如果是CAM模式，检查连接状态
    if (page.data.camMode && page.data.camStatus !== 'connected') {
      console.log('CAM模式下应用显示，检查连接状态');
      page.retryCamConnection();
    }
  },

  /**
   * 应用隐藏处理
   */
  handleAppHide: function(page) {
    console.log('小程序进入后台');
    page._pageHidden = true;
  },

  /**
   * 启动FPS计数器
   */
  startFPSCounter: function(page) {
    lastFrameTime = Date.now();
    frameCount = 0;
    
    // 每秒更新一次FPS显示
    fpsUpdateInterval = setInterval(() => {
      const currentTime = Date.now();
      const deltaTime = currentTime - lastFrameTime;
      
      if (deltaTime > 0) {
        const fps = Math.round((frameCount * 1000) / deltaTime);
        page.setData({
          currentFPS: fps
        });
      }
      
      // 重置计数器
      lastFrameTime = currentTime;
      frameCount = 0;
    }, 1000);
  },

  /**
   * 更新帧计数
   */
  updateFrameCount: function() {
    frameCount++;
  },

  /**
   * 停止FPS计数器
   */
  stopFPSCounter: function() {
    if (fpsUpdateInterval) {
      clearInterval(fpsUpdateInterval);
      fpsUpdateInterval = null;
    }
  }
};

module.exports = lifecycleManager;
