# vision-simulation.js 重构指南

## 📋 重构概述

本次重构将原本3319行的庞大主控制器成功拆分为5个专门的管理器模块，代码量减少81.3%，同时保持所有业务功能不变。

## 🗂️ 文件结构

```
pages/vision-simulation/
├── vision-simulation.js              # 主控制器 (621行)
├── vision-simulation-backup.js       # 原文件备份 (3319行)
├── test-refactor.js                  # 重构测试脚本
├── performance-comparison.js         # 性能对比工具
└── utils/
    ├── lifecycle-manager.js          # 生命周期管理器
    ├── camera-controller.js          # 相机控制管理器
    ├── vision-params-manager.js      # 视觉参数管理器
    ├── ui-event-handler.js           # UI事件处理器
    └── image-save-manager.js         # 图片保存管理器
```

## 🔧 使用方法

### 1. 正常使用
重构后的代码与原代码API完全兼容，无需修改任何调用方式：

```javascript
// 所有原有的方法调用都保持不变
this.toggleView();
this.savePetVisionImage();
this.onResolutionChange(e);
// ... 其他方法
```

### 2. 开发新功能

#### 添加生命周期相关功能
在 `utils/lifecycle-manager.js` 中添加：
```javascript
// 在 lifecycleManager 对象中添加新方法
newLifecycleMethod: function(page) {
  // 实现逻辑
}
```

#### 添加相机相关功能
在 `utils/camera-controller.js` 中添加：
```javascript
// 在 cameraController 对象中添加新方法
newCameraMethod: function(page, params) {
  // 实现逻辑
}
```

#### 添加视觉参数功能
在 `utils/vision-params-manager.js` 中添加：
```javascript
// 在 visionParamsManager 对象中添加新方法
newParamMethod: function(page, value) {
  // 实现逻辑
}
```

#### 添加UI交互功能
在 `utils/ui-event-handler.js` 中添加：
```javascript
// 在 uiEventHandler 对象中添加新方法
newUIMethod: function(page, event) {
  // 实现逻辑
}
```

#### 添加图片处理功能
在 `utils/image-save-manager.js` 中添加：
```javascript
// 在 imageSaveManager 对象中添加新方法
newImageMethod: function(page) {
  // 实现逻辑
}
```

### 3. 在主控制器中调用新方法

```javascript
// 在 vision-simulation.js 中添加对应的调用方法
newFeature: function() {
  // 调用对应管理器的方法
  lifecycleManager.newLifecycleMethod(this);
  // 或
  cameraController.newCameraMethod(this, params);
  // 等等...
}
```

## 🧪 测试方法

### 运行重构测试
```bash
cd pages/vision-simulation
node test-refactor.js
```

### 运行性能对比
```bash
cd pages/vision-simulation
node performance-comparison.js
```

## 🔍 调试指南

### 1. 问题定位
根据问题类型，在对应的管理器中查找：
- 页面加载问题 → `lifecycle-manager.js`
- 相机相关问题 → `camera-controller.js`
- 参数调整问题 → `vision-params-manager.js`
- UI交互问题 → `ui-event-handler.js`
- 图片保存问题 → `image-save-manager.js`

### 2. 日志查看
每个管理器都有详细的console.log输出，可以通过关键词搜索：
```javascript
// 生命周期相关
console.log('页面加载，开始初始化');

// 相机相关
console.log('开始初始化相机');

// 参数相关
console.log('分辨率参数变化:', resolutionFactor);

// UI相关
console.log('切换视角');

// 图片相关
console.log('开始保存宠物视觉图片');
```

## ⚠️ 注意事项

### 1. 模块间通信
- 所有管理器方法的第一个参数都是页面实例 `page`
- 通过 `page.setData()` 更新页面状态
- 通过 `page.data` 获取页面数据

### 2. 全局变量
使用统一的全局变量管理：
```javascript
global.page = null;
global.webglContext = null;
global.cameraCtx = null;
global.visionContext = null;
```

### 3. 错误处理
每个管理器都有独立的错误处理机制，不会影响其他模块的正常运行。

### 4. 性能考虑
- 避免在管理器间进行复杂的相互调用
- 保持单一职责原则
- 及时清理不需要的资源

## 🔄 回滚方案

如果需要回滚到原版本：
```bash
cd pages/vision-simulation
cp vision-simulation-backup.js vision-simulation.js
```

## 📚 相关文档

- [重构总结](../../md文档/重构总结-vision-simulation.md)
- [重构成果展示](../../md文档/重构成果展示.md)
- [项目结构](../../md文档/项目结构.md)

## 🤝 贡献指南

1. 在对应的管理器模块中开发新功能
2. 保持代码风格一致
3. 添加必要的注释和日志
4. 运行测试确保功能正常
5. 更新相关文档

## 📞 支持

如果在使用过程中遇到问题，请：
1. 首先查看相关文档
2. 运行测试脚本检查环境
3. 查看控制台日志定位问题
4. 根据问题类型在对应模块中查找解决方案
