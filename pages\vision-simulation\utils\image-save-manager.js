/**
 * 图片保存管理器
 * 负责处理图片保存相关功能
 */

const imageSaveManager = {

  /**
   * 保存宠物视觉图片
   */
  savePetVisionImage: function(page) {
    const that = page; // 保存 this 指向
    
    console.log('开始保存宠物视觉图片');
    
    // 显示加载提示
    wx.showLoading({
      title: '正在保存图片...',
      mask: true
    });
    
    // 检查是否有WebGL画布
    if (!global.webglContext || !global.webglContext.canvas) {
      wx.hideLoading();
      wx.showToast({
        title: '画布未初始化',
        icon: 'error',
        duration: 2000
      });
      return;
    }
    
    try {
      // 获取当前视角状态
      const currentView = that.data.currentView;
      const breedName = that.data.breedName || '未知品种';
      
      // 如果当前是人类视角，先切换到宠物视角
      if (currentView === 'human') {
        console.log('当前是人类视角，切换到宠物视角进行保存');
        
        // 临时切换到宠物视角
        that.setData({
          currentView: 'dog'
        });
        
        // 等待视角切换完成后再保存
        setTimeout(() => {
          this._saveCanvasToAlbum(that, breedName, () => {
            // 保存完成后恢复原视角
            that.setData({
              currentView: 'human'
            });
          });
        }, 100);
      } else {
        // 当前已经是宠物视角，直接保存
        this._saveCanvasToAlbum(that, breedName);
      }
      
    } catch (error) {
      console.error('保存图片时出错:', error);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败',
        icon: 'error',
        duration: 2000
      });
    }
  },

  /**
   * 将画布内容保存到相册
   */
  _saveCanvasToAlbum: function(page, breedName, callback) {
    const that = page;
    
    try {
      // 获取WebGL画布
      const canvas = global.webglContext.canvas;
      
      if (!canvas) {
        throw new Error('WebGL画布不存在');
      }
      
      console.log('开始从WebGL画布导出图片');
      
      // 从WebGL画布导出图片数据
      wx.canvasToTempFilePath({
        canvas: canvas,
        success: function(res) {
          console.log('画布导出成功，临时文件路径:', res.tempFilePath);
          
          // 保存到相册
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: function(saveRes) {
              console.log('图片保存到相册成功');
              wx.hideLoading();
              
              // 显示成功提示
              wx.showModal({
                title: '保存成功',
                content: `${breedName}的视觉图片已保存到相册`,
                showCancel: false,
                confirmText: '好的',
                success: function() {
                  // 执行回调
                  if (callback && typeof callback === 'function') {
                    callback();
                  }
                }
              });
            },
            fail: function(error) {
              console.error('保存到相册失败:', error);
              wx.hideLoading();
              
              // 检查是否是权限问题
              if (error.errMsg && error.errMsg.includes('auth')) {
                wx.showModal({
                  title: '需要相册权限',
                  content: '保存图片需要访问您的相册，请在设置中开启相册权限',
                  confirmText: '去设置',
                  cancelText: '取消',
                  success: function(modalRes) {
                    if (modalRes.confirm) {
                      wx.openSetting({
                        success: function(settingRes) {
                          console.log('设置页面返回:', settingRes);
                        }
                      });
                    }
                  }
                });
              } else {
                wx.showToast({
                  title: '保存失败',
                  icon: 'error',
                  duration: 2000
                });
              }
              
              // 执行回调
              if (callback && typeof callback === 'function') {
                callback();
              }
            }
          });
        },
        fail: function(error) {
          console.error('画布导出失败:', error);
          wx.hideLoading();
          
          wx.showToast({
            title: '导出图片失败',
            icon: 'error',
            duration: 2000
          });
          
          // 执行回调
          if (callback && typeof callback === 'function') {
            callback();
          }
        }
      });
      
    } catch (error) {
      console.error('保存画布到相册时出错:', error);
      wx.hideLoading();
      
      wx.showToast({
        title: '保存失败: ' + error.message,
        icon: 'error',
        duration: 2000
      });
      
      // 执行回调
      if (callback && typeof callback === 'function') {
        callback();
      }
    }
  },

  /**
   * 检查相册权限
   */
  checkAlbumPermission: function(callback) {
    wx.getSetting({
      success: function(res) {
        if (res.authSetting['scope.writePhotosAlbum'] === false) {
          // 用户之前拒绝了权限，需要引导用户手动开启
          wx.showModal({
            title: '需要相册权限',
            content: '保存图片需要访问您的相册，请在设置中开启相册权限',
            confirmText: '去设置',
            cancelText: '取消',
            success: function(modalRes) {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: function(settingRes) {
                    if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      // 权限开启成功
                      if (callback) callback(true);
                    } else {
                      // 用户仍然拒绝权限
                      if (callback) callback(false);
                    }
                  },
                  fail: function() {
                    if (callback) callback(false);
                  }
                });
              } else {
                if (callback) callback(false);
              }
            }
          });
        } else if (res.authSetting['scope.writePhotosAlbum'] === undefined) {
          // 用户还没有授权过，可以直接调用授权
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: function() {
              if (callback) callback(true);
            },
            fail: function() {
              if (callback) callback(false);
            }
          });
        } else {
          // 用户已经授权
          if (callback) callback(true);
        }
      },
      fail: function() {
        if (callback) callback(false);
      }
    });
  },

  /**
   * 分享应用消息
   */
  onShareAppMessage: function(page) {
    const breedName = page.data.breedName || '爱宠';
    
    return {
      title: '爱宠视觉',
      desc: `看看${breedName}眼中的世界是什么样的`,
      path: '/pages/index/index',
      imageUrl: '/images/share-image.png'
    };
  }
};

module.exports = imageSaveManager;
