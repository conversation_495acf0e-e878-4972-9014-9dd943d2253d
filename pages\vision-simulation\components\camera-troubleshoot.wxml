<!-- 相机问题排查组件 -->
<view class="camera-troubleshoot-container" wx:if="{{showTroubleshoot}}">
  <view class="troubleshoot-overlay" bindtap="closeTroubleshoot"></view>
  
  <view class="troubleshoot-panel">
    <!-- 标题栏 -->
    <view class="troubleshoot-header">
      <text class="troubleshoot-title">相机问题排查</text>
      <view class="close-btn" bindtap="closeTroubleshoot">
        <text class="close-icon">✕</text>
      </view>
    </view>
    
    <!-- 诊断状态 -->
    <view class="diagnosis-status">
      <view class="status-item">
        <text class="status-label">相机权限:</text>
        <text class="status-value {{diagnosis.hasPermission ? 'success' : 'error'}}">
          {{diagnosis.hasPermission ? '✅ 已授权' : '❌ 未授权'}}
        </text>
      </view>
      
      <view class="status-item">
        <text class="status-label">设备兼容:</text>
        <text class="status-value {{diagnosis.webglSupported ? 'success' : 'error'}}">
          {{diagnosis.webglSupported ? '✅ 支持' : '❌ 不支持'}}
        </text>
      </view>
      
      <view class="status-item">
        <text class="status-label">相机可用:</text>
        <text class="status-value {{diagnosis.cameraAvailable ? 'success' : 'error'}}">
          {{diagnosis.cameraAvailable ? '✅ 可用' : '❌ 不可用'}}
        </text>
      </view>
    </view>
    
    <!-- 问题列表 -->
    <view class="issues-section" wx:if="{{diagnosis.issues.length > 0}}">
      <text class="section-title">🔍 发现的问题</text>
      <view class="issues-list">
        <view class="issue-item" wx:for="{{diagnosis.issues}}" wx:key="index">
          <text class="issue-icon">⚠️</text>
          <text class="issue-text">{{item}}</text>
        </view>
      </view>
    </view>
    
    <!-- 解决方案 -->
    <view class="solutions-section" wx:if="{{diagnosis.solutions.length > 0}}">
      <text class="section-title">💡 建议解决方案</text>
      <view class="solutions-list">
        <view class="solution-item" wx:for="{{diagnosis.solutions}}" wx:key="index">
          <text class="solution-number">{{index + 1}}.</text>
          <text class="solution-text">{{item}}</text>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="troubleshoot-actions">
      <!-- 自动修复按钮 -->
      <view class="action-btn primary-btn" bindtap="autoFix" wx:if="{{!fixing}}">
        <text class="btn-icon">🔧</text>
        <text class="btn-text">自动修复</text>
      </view>
      
      <!-- 修复中状态 -->
      <view class="action-btn fixing-btn" wx:if="{{fixing}}">
        <view class="loading-spinner"></view>
        <text class="btn-text">修复中...</text>
      </view>
      
      <!-- 手动设置按钮 -->
      <view class="action-btn secondary-btn" bindtap="manualSetting">
        <text class="btn-icon">⚙️</text>
        <text class="btn-text">手动设置</text>
      </view>
      
      <!-- 重新诊断按钮 -->
      <view class="action-btn tertiary-btn" bindtap="reDiagnose">
        <text class="btn-icon">🔄</text>
        <text class="btn-text">重新诊断</text>
      </view>
    </view>
    
    <!-- 帮助信息 -->
    <view class="help-section">
      <text class="help-title">📚 常见问题</text>
      <view class="help-items">
        <view class="help-item" bindtap="showHelp" data-type="permission">
          <text class="help-question">如何开启相机权限？</text>
          <text class="help-arrow">→</text>
        </view>
        
        <view class="help-item" bindtap="showHelp" data-type="compatibility">
          <text class="help-question">设备不兼容怎么办？</text>
          <text class="help-arrow">→</text>
        </view>
        
        <view class="help-item" bindtap="showHelp" data-type="troubleshoot">
          <text class="help-question">其他问题排查</text>
          <text class="help-arrow">→</text>
        </view>
      </view>
    </view>
    
    <!-- 底部信息 -->
    <view class="troubleshoot-footer">
      <text class="footer-text">如问题仍未解决，请联系技术支持</text>
    </view>
  </view>
</view>
