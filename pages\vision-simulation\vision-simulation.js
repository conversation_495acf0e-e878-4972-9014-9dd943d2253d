// 获取应用实例
const app = getApp();

// 导入核心模块
const webglRenderer = require('./utils/webgl-renderer');
const visionProcessor = require('./utils/vision-processor');
const uiController = require('./utils/ui-controller');
const utils = require('./utils/utils');
const cameraManager = require('./utils/camera-manager');
const visionManager = require('./utils/vision-manager');
const visionConfig = require('./utils/vision-config');
const tipsManager = require('./utils/tips-manager');
const camManager = require('./utils/cam-manager');

// 导入新的管理器模块
const lifecycleManager = require('./utils/lifecycle-manager');
const cameraController = require('./utils/camera-controller');
const visionParamsManager = require('./utils/vision-params-manager');
const uiEventHandler = require('./utils/ui-event-handler');
const imageSaveManager = require('./utils/image-save-manager');
const cameraFix = require('./utils/camera-fix');

// 导入相机修复脚本
require('./camera-init-fix');

// 全局变量，用于存储页面实例和WebGL上下文
global.page = null;
global.webglContext = null;
global.cameraCtx = null;
global.visionContext = null; // 视觉管理器上下文

Page({
  data: {
    // 基础状态
    cameraLoading: false,
    cameraError: false,
    cameraErrorMsg: '',
    currentView: 'dog', // 'human' 或 'dog'
    
    // 品种信息
    breedName: '',
    breedDetails: null,
    
    // 相机配置
    cameraPosition: 'back', // 'front' 或 'back'
    currentCameraType: 'back', // 'front', 'back', 'cam'
    currentResolutionIndex: 0,
    resolutionOptions: [
      { name: '标清', width: 1280, height: 720 },
      { name: '高清', width: 1920, height: 1080 },
      { name: '超清', width: 2560, height: 1440 }
    ],
    
    // 视觉功能特性
    features: {
      color: true,    // 二色视觉
      night: true,    // 夜视能力
      motion: false,  // 运动视觉
      isCat: false    // 是否为猫科动物
    },
    
    // 视觉参数
    dogVisionParams: {
      resolutionFactor: 0.5,      // 清晰度因子
      antiAliasFactor: 0.3,       // 视野因子
      brightness: 1.5,            // 亮度增强
      contrast: 1.2,              // 对比度
      motionSensitivity: 0.8,     // 运动敏感度
      motionThreshold: 0.1,       // 运动阈值
      motionSizeThreshold: 0.05   // 运动物体大小阈值
    },
    
    // 原始参数（用于重置）
    originalDogVisionParams: {
      resolutionFactor: 0.5,
      antiAliasFactor: 0.3,
      brightness: 1.5,
      contrast: 1.2,
      motionSensitivity: 0.8,
      motionThreshold: 0.1,
      motionSizeThreshold: 0.05
    },
    
    // UI状态
    showControls: true,
    showCameraSelector: false,
    showVisionParams: false,
    showResolutionSelector: false,
    showVisionModeSelector: false,
    showVisionModeDetail: false,
    isAnalysisExpanded: false,
    
    // 视觉模式
    currentVisionMode: 'nightVision', // 'dichromatic', 'acuity', 'nightVision', 'motionVision'
    
    // 光照状态
    averageBrightness: 0,
    isLowLight: false,
    isBrightLight: false,
    brightPixelRatio: 0,
    brightPixelRatioFormatted: '0.0',
    LOW_LIGHT_THRESHOLD: 50,
    HIGH_LIGHT_THRESHOLD: 200,
    
    // CAM相关
    camMode: false,
    camStatus: 'disconnected', // 'connecting', 'connected', 'error', 'disconnected'
    camImageUrl: '',
    camErrorMsg: '',
    
    // 性能监控
    currentFPS: 0,
    devicePerformance: 'medium',
    
    // 帧信息
    frame: {
      width: 1280,
      height: 720
    },
    
    // 主题相关
    currentTheme: 'theme1',
    themeStyle: {},
    
    // 提示相关
    showMotionTip: false,
    showBrightLightTip: false,
    showNightVisionTip: false,
    showVisionGuide: false,
    rememberVisionGuide: false
  },

  // 页面生命周期方法
  onLoad: function(options) {
    lifecycleManager.onLoad(this, options);
  },

  onReady: function() {
    lifecycleManager.onReady(this);
  },

  onShow: function() {
    lifecycleManager.onShow(this);
  },

  onHide: function() {
    lifecycleManager.onHide(this);
  },

  onUnload: function() {
    lifecycleManager.onUnload(this);
  },

  // 应用显示/隐藏处理
  _handleAppShow: function() {
    lifecycleManager.handleAppShow(this);
  },

  _handleAppHide: function() {
    lifecycleManager.handleAppHide(this);
  },

  // FPS计数器
  startFPSCounter: function() {
    lifecycleManager.startFPSCounter(this);
  },

  // 相机相关方法
  initializeCameraType: function() {
    cameraController.initializeCameraType(this);
  },

  initCamera: function() {
    cameraController.initCamera(this);
  },

  handleCameraError: function(error) {
    cameraController.handleCameraError(this, error);
  },

  openCameraSetting: function() {
    cameraController.openCameraSetting(this);
  },

  retryCameraInit: function() {
    cameraController.retryCameraInit(this);
  },

  // 相机修复相关方法
  diagnoseCameraIssue: function() {
    return cameraFix.diagnoseCameraIssue(this);
  },

  autoFixCamera: function() {
    return cameraFix.autoFixCamera(this);
  },

  guideUserToEnablePermission: function() {
    return cameraFix.guideUserToEnablePermission(this);
  },

  // 智能相机修复 - 一键解决相机问题
  smartCameraFix: function() {
    console.log('开始智能相机修复...');

    // 显示修复进度
    wx.showLoading({
      title: '正在修复相机...',
      mask: true
    });

    this.autoFixCamera()
      .then((result) => {
        wx.hideLoading();

        if (result.success) {
          wx.showToast({
            title: result.message,
            icon: 'success',
            duration: 2000
          });
        } else if (result.needManualSetting) {
          // 需要手动设置权限
          this.guideUserToEnablePermission();
        } else {
          wx.showToast({
            title: result.message,
            icon: 'none',
            duration: 3000
          });
        }
      })
      .catch((error) => {
        wx.hideLoading();
        console.error('智能修复失败:', error);

        wx.showModal({
          title: '修复失败',
          content: error.message || '相机修复失败，请尝试手动解决',
          showCancel: false,
          confirmText: '知道了'
        });
      });
  },

  resetCameraState: function() {
    cameraController.resetCameraState(this);
  },

  selectCameraType: function(e) {
    const cameraType = e.currentTarget.dataset.type;
    cameraController.selectCameraType(this, cameraType);
  },

  switchToCamera: function(cameraType) {
    cameraController.switchToCamera(this, cameraType);
  },

  switchToPhoneCamera: function(position) {
    cameraController.switchToPhoneCamera(this, position);
  },

  switchToHardwareCamera: function() {
    cameraController.switchToHardwareCamera(this);
  },

  reinitializeCamera: function() {
    cameraController.reinitializeCamera(this);
  },

  changeResolution: function(e) {
    const index = e.currentTarget.dataset.index;
    cameraController.changeResolution(this, index);
  },

  // 视觉参数相关方法
  setBreedVisualAcuity: function(breedName, animalType) {
    visionParamsManager.setBreedVisualAcuity(this, breedName, animalType);
  },

  setSiameseNightVision: function() {
    visionParamsManager.setSiameseNightVision(this);
  },

  setHuskyNightVision: function() {
    visionParamsManager.setHuskyNightVision(this);
  },

  onResolutionChange: function(e) {
    visionParamsManager.onResolutionChange(this, e.detail.value);
  },

  onAntiAliasChange: function(e) {
    visionParamsManager.onAntiAliasChange(this, e.detail.value);
  },

  onBrightnessChange: function(e) {
    visionParamsManager.onBrightnessChange(this, e.detail.value);
  },

  onContrastChange: function(e) {
    visionParamsManager.onContrastChange(this, e.detail.value);
  },

  onMotionSensitivityChange: function(e) {
    visionParamsManager.onMotionSensitivityChange(this, e.detail.value);
  },

  onMotionThresholdChange: function(e) {
    visionParamsManager.onMotionThresholdChange(this, e.detail.value);
  },

  onMotionSizeThresholdChange: function(e) {
    visionParamsManager.onMotionSizeThresholdChange(this, e.detail.value);
  },

  resetVisionParams: function() {
    visionParamsManager.resetVisionParams(this);
  },

  // UI事件处理方法
  toggleView: function() {
    uiEventHandler.toggleView(this);
  },

  handleTouchStart: function(e) {
    uiEventHandler.handleTouchStart(this, e);
  },

  handleTouchEnd: function(e) {
    uiEventHandler.handleTouchEnd(this, e);
  },

  handleViewAreaTap: function() {
    uiEventHandler.handleViewAreaTap(this);
  },

  toggleAnalysisExpand: function() {
    uiEventHandler.toggleAnalysisExpand(this);
  },

  toggleControlPanel: function() {
    uiEventHandler.toggleControlPanel(this);
  },

  toggleCameraSelector: function() {
    uiEventHandler.toggleCameraSelector(this);
  },

  toggleVisionParams: function() {
    uiEventHandler.toggleVisionParams(this);
  },

  toggleResolutionSelector: function() {
    uiEventHandler.toggleResolutionSelector(this);
  },

  toggleVisionModeSelector: function() {
    uiEventHandler.toggleVisionModeSelector(this);
  },

  preventTouchMove: function(e) {
    return uiEventHandler.preventTouchMove(e);
  },

  selectVisionMode: function(e) {
    const mode = e.currentTarget.dataset.mode;
    uiEventHandler.selectVisionMode(this, mode);
  },

  toggleMotionVision: function() {
    uiEventHandler.toggleMotionVision(this);
  },

  showMotionTip: function() {
    uiEventHandler.showMotionTip(this);
  },

  closeMotionTip: function() {
    uiEventHandler.closeMotionTip(this);
  },

  showBrightLightTip: function() {
    uiEventHandler.showBrightLightTip(this);
  },

  closeBrightLightTip: function() {
    uiEventHandler.closeBrightLightTip(this);
  },

  showNightVisionTip: function() {
    uiEventHandler.showNightVisionTip(this);
  },

  closeNightVisionTip: function() {
    uiEventHandler.closeNightVisionTip(this);
  },

  changeTab: function(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    uiEventHandler.changeTab(this, index);
  },

  swiperChange: function(e) {
    uiEventHandler.swiperChange(this, e);
  },

  preventBubble: function(e) {
    return uiEventHandler.preventBubble(e);
  },

  navigateBack: function() {
    uiEventHandler.navigateBack();
  },

  // 图片保存方法
  savePetVisionImage: function() {
    imageSaveManager.savePetVisionImage(this);
  },

  onShareAppMessage: function() {
    return imageSaveManager.onShareAppMessage(this);
  },

  // 核心处理方法 - 保留原有逻辑
  processFrameWebGL: function(frame) {
    try {
      // 如果WebGL上下文未初始化或页面已卸载，则不处理
      if (!global.webglContext || !global.page || !global.visionContext) {
        console.warn('处理帧跳过: WebGL上下文未初始化或页面已卸载');
        return;
      }

      // CAM模式下，图片流处理方式不同，直接返回
      if (this.data.camMode) {
        console.log('CAM模式下不进行WebGL视觉处理');
        return;
      }

      // 更新最后帧处理时间戳，用于检测相机工作状态
      this._lastFrameCheckTime = Date.now();

      // 保存当前帧用于参数调整时重新渲染
      this._lastFrame = frame;

      // 更新帧计数
      lifecycleManager.updateFrameCount();

      // 获取当前分辨率设置
      const currentResolution = this.data.resolutionOptions[this.data.currentResolutionIndex];

      // 更新帧尺寸显示
      this.setData({
        frame: {
          width: currentResolution.width,
          height: currentResolution.height
        }
      });

      // 使用视觉管理器处理帧 - 修复参数顺序
      const animalType = this.data.features.isCat ? 'cat' : 'dog';
      const motionParams = {
        enabled: this.data.features.motion,
        sensitivity: this.data.dogVisionParams.motionSensitivity,
        threshold: this.data.dogVisionParams.motionThreshold,
        sizeThreshold: this.data.dogVisionParams.motionSizeThreshold
      };

      const processResult = visionManager.processFrame(
        frame,
        global.webglContext,
        animalType,
        motionParams
      );

      if (processResult) {
        // 处理成功，更新相关状态
        if (processResult.brightness !== undefined) {
          this.setData({
            averageBrightness: Math.round(processResult.brightness),
            isLowLight: processResult.brightness < this.data.LOW_LIGHT_THRESHOLD,
            isBrightLight: processResult.brightness > this.data.HIGH_LIGHT_THRESHOLD
          });
        }
      }

    } catch (error) {
      console.error('WebGL帧处理出错:', error);
      // 错误处理，但不中断应用
    }
  },

  // 主题更新方法
  updateTheme: function() {
    // 从全局应用实例中获取当前主题
    const currentTheme = app.globalData.currentTheme || 'theme1';
    const themeConfig = app.globalData.themeConfig;

    if (themeConfig) {
      this.setData({
        currentTheme: currentTheme,
        themeStyle: {
          background: themeConfig.gradient,
          backgroundDark: themeConfig.darkBg,
          cardBg: themeConfig.cardBg
        }
      });

      // 设置导航栏颜色
      wx.setNavigationBarColor({
        frontColor: '#ffffff',
        backgroundColor: themeConfig.darkBg,
        fail: function(err) {
          console.error('设置导航栏颜色失败:', err);
        }
      });
    }
  },

  // 主题回调注册
  registerThemeCallback: function() {
    // 先移除可能存在的旧回调，避免重复
    this.unregisterThemeCallback();

    // 注册新的回调
    if (app.themeChangeCallbacks) {
      app.themeChangeCallbacks.push(() => {
        this.updateTheme();
      });
    }
  },

  unregisterThemeCallback: function() {
    if (app.themeChangeCallbacks) {
      const index = app.themeChangeCallbacks.findIndex(callback =>
        callback.toString().includes('this.updateTheme')
      );
      if (index > -1) {
        app.themeChangeCallbacks.splice(index, 1);
      }
    }
  },

  // CAM相关方法
  initCamManager: function() {
    console.log('初始化CAM管理器');

    // 设置CAM事件回调
    camManager.setEventCallbacks({
      onStatusChange: (status, message) => {
        this.setData({
          camStatus: status,
          camErrorMsg: message || ''
        });
      },
      onImageUpdate: (imageUrl) => {
        this.setData({
          camImageUrl: imageUrl
        });
      }
    });
  },

  enableCamMode: function() {
    console.log('启用CAM模式');

    // 先停止手机相机
    if (this.cameraCtx) {
      try {
        this.cameraCtx.stop();
        this.cameraCtx = null;
      } catch (error) {
        console.error('停止手机相机时出错:', error);
      }
    }

    // 设置CAM模式状态
    this.setData({
      camMode: true,
      currentCameraType: 'cam',
      cameraLoading: false,
      cameraError: false
    });

    // 启动CAM连接
    camManager.connect()
      .then(() => {
        this.setData({
          camStatus: 'connected'
        });
      })
      .catch((error) => {
        this.setData({
          camStatus: 'error',
          camErrorMsg: error.message || 'CAM连接失败'
        });

        // 连接失败，自动回退到手机相机
        setTimeout(() => {
          this.disableCamMode();
        }, 2000);
      });
  },

  disableCamMode: function() {
    console.log('禁用CAM模式');

    // 断开CAM连接
    camManager.disconnectCam();

    // 清理CAM相关状态
    this.setData({
      camMode: false,
      camStatus: 'disconnected',
      camImageUrl: '',
      camErrorMsg: '',
      currentCameraType: this.data.cameraPosition || 'back'
    });

    // 恢复手机相机
    setTimeout(() => {
      this.initCamera();
    }, 500);
  },

  // 其他必要方法
  showVisionSelectGuide: function() {
    // 引导功能已禁用
    return;
  },

  checkShowVisionGuide: function() {
    // 从本地存储中读取用户设置
    const hideVisionGuide = wx.getStorageSync('hideVisionGuide');

    if (!hideVisionGuide) {
      this.setData({
        showVisionGuide: true
      });
    }
  },

  // 内存管理方法
  _forceMemoryCleanup: function() {
    try {
      console.log('执行强制内存清理');

      // 清理图片缓存
      this._forceCamImageCleanup();

      // 强制垃圾回收（如果支持）
      if (typeof global.gc === 'function') {
        global.gc();
      }

    } catch (error) {
      console.error('强制内存清理失败:', error);
    }
  },

  _forceCamImageCleanup: function() {
    try {
      console.log('清理CAM图片缓存');

      // 清理CAM图片URL
      this.setData({
        camImageUrl: '',
        camNextImageUrl: ''
      });

    } catch (error) {
      console.error('清理CAM图片缓存失败:', error);
    }
  },

  _monitorMemoryUsage: function() {
    try {
      if (typeof wx.getSystemInfo === 'function') {
        wx.getSystemInfo({
          success: (res) => {
            console.log('系统信息:', res);
          }
        });
      }
    } catch (error) {
      console.error('内存监控失败:', error);
    }
  }

});
