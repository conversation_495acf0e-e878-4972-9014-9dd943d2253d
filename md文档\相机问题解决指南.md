# 相机问题解决指南

## 🚨 常见相机问题

### 问题现象
- 相机启动失败，显示"相机启动失败，请检查相机权限或重启应用"
- 黑屏无视频流
- 显示"相机权限被拒绝"
- 相机加载中状态一直不消失

## 🔍 问题诊断

### 自动诊断
应用已集成智能诊断功能，当相机出现问题时会自动弹出修复选项：

1. **自动修复** - 系统会自动检测并尝试修复问题
2. **手动处理** - 用户可以按照指南手动解决

### 手动诊断步骤

#### 1. 检查相机权限
```
设置路径：微信 > 设置 > 隐私与安全 > 授权管理 > [小程序名称] > 相机
```

#### 2. 检查系统权限
```
iOS: 设置 > 隐私与安全 > 相机 > 微信
Android: 设置 > 应用管理 > 微信 > 权限管理 > 相机
```

#### 3. 检查设备兼容性
- 确认设备支持相机功能
- 确认设备支持WebGL（用于视觉处理）
- 检查微信版本是否过旧

## 🛠️ 解决方案

### 方案一：智能自动修复 ⭐ 推荐
1. 当出现相机问题时，点击"自动修复"按钮
2. 系统会自动检测问题并尝试修复
3. 如需手动授权，会自动跳转到设置页面

### 方案二：手动权限设置
1. **小程序内设置**
   - 点击相机错误提示中的"前往设置"按钮
   - 在授权管理中开启相机权限
   - 返回小程序，相机会自动重新初始化

2. **微信设置**
   - 打开微信 > 我 > 设置 > 隐私与安全 > 授权管理
   - 找到对应小程序，开启相机权限
   - 重新进入小程序

3. **系统设置**
   - iOS: 设置 > 隐私与安全 > 相机 > 微信 > 开启
   - Android: 设置 > 应用管理 > 微信 > 权限管理 > 相机 > 允许

### 方案三：重启恢复
1. **重启小程序**
   - 完全退出小程序（从微信聊天界面删除）
   - 重新打开小程序

2. **重启微信**
   - 完全退出微信应用
   - 重新打开微信和小程序

3. **重启设备**
   - 关机重启手机
   - 重新打开微信和小程序

### 方案四：清理缓存
1. **清理小程序缓存**
   - 微信 > 我 > 设置 > 通用 > 存储空间 > 缓存 > 清理
   - 重新打开小程序

2. **清理微信缓存**
   - 微信 > 我 > 设置 > 通用 > 存储空间 > 清理微信缓存
   - 重启微信

## 🔧 技术细节

### 权限申请流程
1. 检查当前权限状态
2. 如未授权，调用 `wx.authorize` 申请权限
3. 如被拒绝，引导用户手动开启
4. 权限获取后自动重新初始化相机

### 错误码说明
- `auth_denied`: 相机权限被拒绝
- `auth_rejected`: 相机权限申请被拒绝
- `camera_occupied`: 相机被其他应用占用
- `camera_context_error`: 相机上下文创建失败
- `get_setting_failed`: 获取权限设置失败

### 自动重试机制
- 最大重试次数：3次
- 重试间隔：指数退避（1秒、2秒、4秒）
- 权限问题会提供智能修复选项

## 📱 设备兼容性

### 支持的设备
- iOS 10.0+ 
- Android 6.0+
- 微信版本 7.0+

### 已知问题
- 部分老旧设备可能不支持WebGL
- 某些定制Android系统权限管理较严格
- 模拟器环境可能无法正常使用相机

## 🆘 故障排除

### 问题：权限已开启但相机仍无法使用
**解决方案：**
1. 检查是否有其他应用占用相机
2. 重启设备释放相机资源
3. 检查设备存储空间是否充足
4. 更新微信到最新版本

### 问题：相机画面卡顿或黑屏
**解决方案：**
1. 检查网络连接状态
2. 关闭其他占用内存的应用
3. 降低相机分辨率设置
4. 重启小程序

### 问题：WebGL初始化失败
**解决方案：**
1. 检查设备是否支持WebGL
2. 更新设备系统版本
3. 清理浏览器缓存
4. 尝试在其他设备上使用

## 📞 技术支持

### 自助诊断
应用内置了完整的诊断工具，可以：
- 自动检测权限状态
- 检测设备兼容性
- 提供针对性解决方案
- 一键自动修复常见问题

### 日志收集
如需技术支持，请提供以下信息：
- 设备型号和系统版本
- 微信版本
- 问题出现的具体步骤
- 控制台错误日志

### 联系方式
- 小程序内反馈功能
- 开发者邮箱：[开发者邮箱]
- 技术支持QQ群：[QQ群号]

## 🎯 预防措施

### 最佳实践
1. **首次使用时主动申请权限**
2. **定期检查权限状态**
3. **提供清晰的权限说明**
4. **优雅处理权限被拒绝的情况**

### 用户教育
1. **说明为什么需要相机权限**
2. **提供详细的设置指南**
3. **建立用户反馈渠道**
4. **持续优化用户体验**

---

> 💡 **提示**: 大部分相机问题都可以通过智能自动修复功能解决。如果自动修复无效，请按照本指南进行手动排查。
